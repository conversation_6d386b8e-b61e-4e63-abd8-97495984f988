{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/components/tabbarsf.vue?d717", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/components/tabbarsf.vue?c591", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/components/tabbarsf.vue?b9ff", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/components/tabbarsf.vue?4428", "uni-app:///components/tabbarsf.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/components/tabbarsf.vue?dc38", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/components/tabbarsf.vue?0350"], "names": ["name", "props", "cur", "type", "default", "data", "activeColor", "lat", "count", "lng", "inactiveColor", "tmplIds", "tabbarConfig", "icon", "value", "path", "mounted", "console", "onLoad", "methods", "isActive", "changeTab", "uni", "url", "infodata", "success", "title", "content", "cancelText", "confirmText", "confirmColor", "withSubscriptions", "fail", "updateTabbarHeight", "query", "select", "boundingClientRect", "exec"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrDA;AAAA;AAAA;AAAA;AAAu1B,CAAgB,u2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCqB32B;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,UACA,IACA,IACA,8CACA;MACAC,eACA;QACAZ;QACAa;QACAC;QACAC;MACA,GACA;QACAf;QACAa;QACAC;QACAC;MACA;IAEA;EACA;EACAC;IACA;IACAC;EACA;EACAC;IACA;IACAD;EACA;EACAE;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QAAA;MAAA;MACA;QACAJ;QACAK;UACAC;QACA;MACA;MACA;MACA;MACA;QACAN;QACA;MACA;MAEA;MACA;QACAO;MACA;QACAP;QACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;UACAA;UACAK;YACAX;YACAc;cACAR;cACA;cACA;gBAAA;cAAA;cACA;gBACAK;kBACAI;kBACAC;kBACAC;kBACAC;kBACAC;kBAAA;kBACAL;oBACA;oBACAH;oBACA;sBACAA;wBACAS;sBACA;oBACA;sBACA;sBACAT;oBACA;kBACA;gBACA;cACA;YACA;YACAU;cACAf;YACA;UACA;QACA;MACA;IACA;IACA;IACAgB;MAAA;MACA;MACAC,MACAC,yBACAC;QACA;UACA;UACA;UACA;UACA;UACA;QACA;MACA,GACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzJA;AAAA;AAAA;AAAA;AAA8lD,CAAgB,kjDAAG,EAAC,C;;;;;;;;;;;ACAlnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/tabbarsf.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tabbarsf.vue?vue&type=template&id=fb3b0a28&scoped=true&\"\nvar renderjs\nimport script from \"./tabbarsf.vue?vue&type=script&lang=js&\"\nexport * from \"./tabbarsf.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tabbarsf.vue?vue&type=style&index=0&id=fb3b0a28&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"fb3b0a28\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/tabbarsf.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tabbarsf.vue?vue&type=template&id=fb3b0a28&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.tabbarConfig.length\n  var l0 = _vm.__map(_vm.tabbarConfig, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = _vm.isActive(item.value)\n    var m1 = _vm.isActive(item.value)\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tabbarsf.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tabbarsf.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"custom-tabbar fixed flex-center bg-base border-top\">\n\t\t<view\n\t\t\tv-for=\"(item, index) in tabbarConfig\"\n\t\t\t:key=\"item.value\"\n\t\t\**********=\"changeTab(item.value)\"\n\t\t\tclass=\"flex-center flex-column\"\n\t\t\t:style=\"{ width: 100 / tabbarConfig.length + '%', color: isActive(item.value) ? activeColor : inactiveColor }\"\n\t\t>\n\t\t\t<view class=\"icon-wrapper\">\n\t\t\t\t<u-icon\n\t\t\t\t\t:name=\"item.icon\"\n\t\t\t\t\t:color=\"isActive(item.value) ? activeColor : inactiveColor\"\n\t\t\t\t\tsize=\"28\"\n\t\t\t\t></u-icon>\n\t\t\t</view>\n\t\t\t<view class=\"tab-text\">{{ item.name }}</view>\n\t\t</view>\n\t</view>\n</template>\n<script>\nexport default {\n\tname: 'CustomTabbar',\n\tprops: {\n\t\tcur: {\n\t\t\ttype: [Number, String],\n\t\t\tdefault: '0'\n\t\t}\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tactiveColor: '#E41F19',\n\t\t\tlat: '',\n\t\t\tcount: 0,\n\t\t\tlng: '',\n\t\t\tinactiveColor: '#666',\n\t\t\ttmplIds: [\n\t\t\t\t'',\n\t\t\t\t'',\n\t\t\t\t'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'\n\t\t\t],\n\t\t\ttabbarConfig: [\n\t\t\t\t{\n\t\t\t\t\tname: '接单大厅',\n\t\t\t\t\ticon: 'fingerprint',\n\t\t\t\t\tvalue: 0,\n\t\t\t\t\tpath: '/shifu/Receiving'\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tname: '我的',\n\t\t\t\t\ticon: 'account-fill',\n\t\t\t\t\tvalue: 1,\n\t\t\t\t\tpath: '/shifu/mine'\n\t\t\t\t}\n\t\t\t]\n\t\t};\n\t},\n\tmounted() {\n\t\tthis.updateTabbarHeight();\n\t\tconsole.log('Current tab:', this.cur);\n\t},\n\tonLoad() {\n\t\tthis.count = uni.getStorageSync('listCount');\n\t\tconsole.log(this.count);\n\t},\n\tmethods: {\n\t\t// 检查是否为激活状态的tab\n\t\tisActive(value) {\n\t\t\treturn String(value) === String(this.cur);\n\t\t},\n\t\t// 切换tab\n\t\tchangeTab(value) {\n\t\t\tif (this.isActive(value)) return;\n\t\t\tconst targetTab = this.tabbarConfig.find(item => String(item.value) === String(value));\n\t\t\tif (targetTab) {\n\t\t\t\tconsole.log('Navigating to:', targetTab.path);\n\t\t\t\tuni.reLaunch({\n\t\t\t\t\turl: targetTab.path\n\t\t\t\t});\n\t\t\t}\n\t\t\t// let infodata = JSON.parse(uni.getStorageSync('shiInfo'));\n\t\t\tconst shiInfoRaw = uni.getStorageSync('shiInfo');\n\t\t\t  if (!shiInfoRaw) {\n\t\t\t    console.warn('shiInfo is empty or not set in storage');\n\t\t\t    return;\n\t\t\t  }\n\t\t\t\n\t\t\t  let infodata;\n\t\t\t  try {\n\t\t\t    infodata = JSON.parse(shiInfoRaw);\n\t\t\t  } catch (e) {\n\t\t\t    console.error('Failed to parse shiInfo:', e);\n\t\t\t    return;\n\t\t\t  }\n\t\t\tif (infodata.status === 2) {\n\t\t\t\tconst panduan = uni.getStorageSync('shiInfo');\n\t\t\t\t// Check if user has previously canceled the subscription prompt\n\t\t\t\tconst hasCanceledSubscription = uni.getStorageSync('hasCanceledSubscription');\n\t\t\t\tconst hasShownModal = uni.getStorageSync('hasShownSubscriptionModal');\n\t\t\t\tif (panduan && !hasCanceledSubscription) {\n\t\t\t\t\tconsole.log(1111);\n\t\t\t\t\tuni.requestSubscribeMessage({\n\t\t\t\t\t\ttmplIds: this.tmplIds,\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tconsole.log('requestSubscribeMessage success:', res, 'with tmplIds:', this.tmplIds);\n\t\t\t\t\t\t\t// Check if any of the template IDs were rejected\n\t\t\t\t\t\t\tconst hasRejection = this.tmplIds.some(tmplId => res[tmplId] === 'reject');\n\t\t\t\t\t\t\tif (hasRejection && !hasShownModal) {\n\t\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\t\t\tcontent: '您已关闭消息订阅，建议点击‘通知管理’开启，方便及时接收用户订单通知。',\n\t\t\t\t\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\t\t\t\t\tconfirmText: '去开启',\n\t\t\t\t\t\t\t\t\tconfirmColor: '#007AFF', // Set confirm button background to blue\n\t\t\t\t\t\t\t\t\tsuccess: (modalRes) => {\n\t\t\t\t\t\t\t\t\t\t// Set flag to prevent showing modal again\n\t\t\t\t\t\t\t\t\t\tuni.setStorageSync('hasShownSubscriptionModal', true);\n\t\t\t\t\t\t\t\t\t\tif (modalRes.confirm) {\n\t\t\t\t\t\t\t\t\t\t\tuni.openSetting({\n\t\t\t\t\t\t\t\t\t\t\t\twithSubscriptions: true\n\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t} else if (modalRes.cancel) {\n\t\t\t\t\t\t\t\t\t\t\t// Set flag in storage when user cancels\n\t\t\t\t\t\t\t\t\t\t\tuni.setStorageSync('hasCanceledSubscription', true);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error('requestSubscribeMessage failed:', err, 'with tmplIds:', this.tmplIds);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t// 计算tabbar高度并更新\n\t\tupdateTabbarHeight() {\n\t\t\tconst query = uni.createSelectorQuery().in(this);\n\t\t\tquery\n\t\t\t\t.select('.custom-tabbar')\n\t\t\t\t.boundingClientRect(data => {\n\t\t\t\t\tif (data) {\n\t\t\t\t\t\tconst systemInfo = uni.getSystemInfoSync();\n\t\t\t\t\t\tconst tabbarHeight = data.height;\n\t\t\t\t\t\tconst windowHeight = systemInfo.windowHeight;\n\t\t\t\t\t\tthis.$emit('update:tabbarHeight', tabbarHeight);\n\t\t\t\t\t\tthis.$emit('update:availableHeight', windowHeight - tabbarHeight);\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\t.exec();\n\t\t}\n\t}\n};\n</script>\n<style scoped lang=\"scss\">\n.custom-tabbar {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\theight: 120rpx;\n\tpadding-bottom: calc(env(safe-area-inset-bottom) / 2);\n\tbackground-color: #fff;\n\tz-index: 1000;\n\tborder-top: 1px solid #eee;\n}\n.tab-text {\n\tfont-size: 22rpx;\n\tmargin-top: 5rpx;\n\tline-height: 32rpx;\n}\n.flex-center {\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n}\n.flex-column {\n\tflex-direction: column;\n}\n.bg-base {\n\tbackground-color: #fff;\n}\n.icon-wrapper {\n\tposition: relative;\n}\n.badge {\n\tposition: absolute;\n\ttop: -5rpx;\n\tright: -8rpx;\n\twidth: 32rpx;\n\theight: 32rpx;\n\tbackground-color: #E41F19;\n\tcolor: #fff;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tfont-size: 20rpx;\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tabbarsf.vue?vue&type=style&index=0&id=fb3b0a28&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tabbarsf.vue?vue&type=style&index=0&id=fb3b0a28&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756166573688\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}