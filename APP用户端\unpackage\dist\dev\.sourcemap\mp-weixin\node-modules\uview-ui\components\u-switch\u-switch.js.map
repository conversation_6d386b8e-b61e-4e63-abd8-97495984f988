{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-switch/u-switch.vue?855e", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-switch/u-switch.vue?a051", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-switch/u-switch.vue?e25c", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-switch/u-switch.vue?0ca6", "uni-app:///node_modules/uview-ui/components/u-switch/u-switch.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-switch/u-switch.vue?d6c3", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-switch/u-switch.vue?b562"], "names": ["name", "mixins", "watch", "value", "immediate", "handler", "uni", "data", "bgColor", "computed", "isActive", "switchStyle", "style", "nodeStyle", "bgStyle", "customInactiveColor", "methods", "clickHandler"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sUAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9CA;AAAA;AAAA;AAAA;AAAu1B,CAAgB,u2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC8B32B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAlBA,eAmBA;EACAA;EACAC;EACAC;IACAC;MACAC;MACAC;QACA;UACAC;QACA;MACA;IACA;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;MACA;MACAC;MACAA;MACA;MACA;MACA;MACA;QACAA;MACA;MACAA;MACA;IACA;IACAC;MACA;MACA;MACAD;MACAA;MACA;MACAA;MACA;IACA;IACAE;MACA;MACA;MACAF;MACAA;MACAA;MACA;MACAA;MACA;IACA;IACAG;MACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;QACA;UACA;QACA;QACA;QACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5HA;AAAA;AAAA;AAAA;AAA8lD,CAAgB,kjDAAG,EAAC,C;;;;;;;;;;;ACAlnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-switch/u-switch.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-switch.vue?vue&type=template&id=4a8c9de7&scoped=true&\"\nvar renderjs\nimport script from \"./u-switch.vue?vue&type=script&lang=js&\"\nexport * from \"./u-switch.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-switch.vue?vue&type=style&index=0&id=4a8c9de7&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4a8c9de7\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-switch/u-switch.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-switch.vue?vue&type=template&id=4a8c9de7&scoped=true&\"", "var components\ntry {\n  components = {\n    uLoadingIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-loading-icon/u-loading-icon\" */ \"uview-ui/components/u-loading-icon/u-loading-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.switchStyle, _vm.$u.addStyle(_vm.customStyle)])\n  var s1 = _vm.__get_style([_vm.bgStyle])\n  var s2 = _vm.__get_style([_vm.nodeStyle])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n        s2: s2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-switch.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-switch.vue?vue&type=script&lang=js&\"", "<template>\n\t<view\n\t    class=\"u-switch\"\n\t    :class=\"[disabled && 'u-switch--disabled']\"\n\t    :style=\"[switchStyle, $u.addStyle(customStyle)]\"\n\t    @tap=\"clickHandler\"\n\t>\n\t\t<view\n\t\t    class=\"u-switch__bg\"\n\t\t    :style=\"[bgStyle]\"\n\t\t>\n\t\t</view>\n\t\t<view\n\t\t    class=\"u-switch__node\"\n\t\t    :class=\"[value && 'u-switch__node--on']\"\n\t\t    :style=\"[nodeStyle]\"\n\t\t    ref=\"u-switch__node\"\n\t\t>\n\t\t\t<u-loading-icon\n\t\t\t    :show=\"loading\"\n\t\t\t    mode=\"circle\"\n\t\t\t    timingFunction='linear'\n\t\t\t    :color=\"value ? activeColor : '#AAABAD'\"\n\t\t\t    :size=\"size * 0.6\"\n\t\t\t/>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport props from './props.js';\n\t/**\n\t * switch 开关选择器\n\t * @description 选择开关一般用于只有两个选择，且只能选其一的场景。\n\t * @tutorial https://www.uviewui.com/components/switch.html\n\t * @property {Boolean}\t\t\t\t\t\tloading\t\t\t是否处于加载中（默认 false ）\n\t * @property {Boolean}\t\t\t\t\t\tdisabled\t\t是否禁用（默认 false ）\n\t * @property {String | Number}\t\t\t\tsize\t\t\t开关尺寸，单位px （默认 25 ）\n\t * @property {String}\t\t\t\t\t\tactiveColor\t\t打开时的背景色 （默认 '#2979ff' ）\n\t * @property {String} \t\t\t\t\t\tinactiveColor\t关闭时的背景色 （默认 '#ffffff' ）\n\t * @property {Boolean | String | Number}\tvalue\t\t\t通过v-model双向绑定的值 （默认 false ）\n\t * @property {Boolean | String | Number}\tactiveValue\t\t打开选择器时通过change事件发出的值 （默认 true ）\n\t * @property {Boolean | String | Number}\tinactiveValue\t关闭选择器时通过change事件发出的值 （默认 false ）\n\t * @property {Boolean}\t\t\t\t\t\tasyncChange\t\t是否开启异步变更，开启后需要手动控制输入值 （默认 false ）\n\t * @property {String | Number}\t\t\t\tspace\t\t\t圆点与外边框的距离 （默认 0 ）\n\t * @property {Object}\t\t\t\t\t\tcustomStyle\t\t定义需要用到的外部样式\n\t *\n\t * @event {Function} change 在switch打开或关闭时触发\n\t * @example <u-switch v-model=\"checked\" active-color=\"red\" inactive-color=\"#eee\"></u-switch>\n\t */\n\texport default {\n\t\tname: \"u-switch\",\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin,props],\n\t\twatch: {\n\t\t\tvalue: {\n\t\t\t\timmediate: true,\n\t\t\t\thandler(n) {\n\t\t\t\t\tif(n !== this.inactiveValue && n !== this.activeValue) {\n\t\t\t\t\t\tuni.$u.error('v-model绑定的值必须为inactiveValue、activeValue二者之一')\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tbgColor: '#ffffff'\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tisActive(){\n\t\t\t\treturn this.value === this.activeValue;\n\t\t\t},\n\t\t\tswitchStyle() {\n\t\t\t\tlet style = {}\n\t\t\t\t// 这里需要加2，是为了腾出边框的距离，否则圆点node会和外边框紧贴在一起\n\t\t\t\tstyle.width = uni.$u.addUnit(this.size * 2 + 2)\n\t\t\t\tstyle.height = uni.$u.addUnit(Number(this.size) + 2)\n\t\t\t\t// style.borderColor = this.value ? 'rgba(0, 0, 0, 0)' : 'rgba(0, 0, 0, 0.12)'\n\t\t\t\t// 如果自定义了“非激活”演示，name边框颜色设置为透明(跟非激活颜色一致)\n\t\t\t\t// 这里不能简单的设置为非激活的颜色，否则打开状态时，会有边框，所以需要透明\n\t\t\t\tif(this.customInactiveColor) {\n\t\t\t\t\tstyle.borderColor = 'rgba(0, 0, 0, 0)'\n\t\t\t\t}\n\t\t\t\tstyle.backgroundColor = this.isActive ? this.activeColor : this.inactiveColor\n\t\t\t\treturn style;\n\t\t\t},\n\t\t\tnodeStyle() {\n\t\t\t\tlet style = {}\n\t\t\t\t// 如果自定义非激活颜色，将node圆点的尺寸减少两个像素，让其与外边框距离更大一点\n\t\t\t\tstyle.width = uni.$u.addUnit(this.size - this.space)\n\t\t\t\tstyle.height = uni.$u.addUnit(this.size - this.space)\n\t\t\t\tconst translateX = this.isActive ? uni.$u.addUnit(this.space) : uni.$u.addUnit(this.size);\n\t\t\t\tstyle.transform = `translateX(-${translateX})`\n\t\t\t\treturn style\n\t\t\t},\n\t\t\tbgStyle() {\n\t\t\t\tlet style = {}\n\t\t\t\t// 这里配置一个多余的元素在HTML中，是为了让switch切换时，有更良好的背景色扩充体验(见实际效果)\n\t\t\t\tstyle.width = uni.$u.addUnit(Number(this.size) * 2 - this.size / 2)\n\t\t\t\tstyle.height = uni.$u.addUnit(this.size)\n\t\t\t\tstyle.backgroundColor = this.inactiveColor\n\t\t\t\t// 打开时，让此元素收缩，否则反之\n\t\t\t\tstyle.transform = `scale(${this.isActive ? 0 : 1})`\n\t\t\t\treturn style\n\t\t\t},\n\t\t\tcustomInactiveColor() {\n\t\t\t\t// 之所以需要判断是否自定义了“非激活”颜色，是为了让node圆点离外边框更宽一点的距离\n\t\t\t\treturn this.inactiveColor !== '#fff' && this.inactiveColor !== '#ffffff'\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tclickHandler() {\n\t\t\t\tif (!this.disabled && !this.loading) {\n\t\t\t\t\tconst oldValue = this.isActive ? this.inactiveValue : this.activeValue\n\t\t\t\t\tif(!this.asyncChange) {\n\t\t\t\t\t\tthis.$emit('input', oldValue)\n\t\t\t\t\t}\n\t\t\t\t\t// 放到下一个生命周期，因为双向绑定的value修改父组件状态需要时间，且是异步的\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tthis.$emit('change', oldValue)\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t};\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t.u-switch {\n\t\t@include flex(row);\n\t\t/* #ifndef APP-NVUE */\n\t\tbox-sizing: border-box;\n\t\t/* #endif */\n\t\tposition: relative;\n\t\tbackground-color: #fff;\n\t\tborder-width: 1px;\n\t\tborder-radius: 100px;\n\t\ttransition: background-color 0.4s;\n\t\tborder-color: rgba(0, 0, 0, 0.12);\n\t\tborder-style: solid;\n\t\tjustify-content: flex-end;\n\t\talign-items: center;\n\t\t// 由于weex为阿里逗着玩的KPI项目，导致bug奇多，这必须要写这一行，\n\t\t// 否则在iOS上，点击页面任意地方，都会触发switch的点击事件\n\t\toverflow: hidden;\n\n\t\t&__node {\n\t\t\t@include flex(row);\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tborder-radius: 100px;\n\t\t\tbackground-color: #fff;\n\t\t\tborder-radius: 100px;\n\t\t\tbox-shadow: 1px 1px 1px 0 rgba(0, 0, 0, 0.25);\n\t\t\ttransition-property: transform;\n\t\t\ttransition-duration: 0.4s;\n\t\t\ttransition-timing-function: cubic-bezier(0.3, 1.05, 0.4, 1.05);\n\t\t}\n\n\t\t&__bg {\n\t\t\tposition: absolute;\n\t\t\tborder-radius: 100px;\n\t\t\tbackground-color: #FFFFFF;\n\t\t\ttransition-property: transform;\n\t\t\ttransition-duration: 0.4s;\n\t\t\tborder-top-left-radius: 0;\n\t\t\tborder-bottom-left-radius: 0;\n\t\t\ttransition-timing-function: ease;\n\t\t}\n\n\t\t&--disabled {\n\t\t\topacity: 0.6;\n\t\t}\n\t}\n</style>\n", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-switch.vue?vue&type=style&index=0&id=4a8c9de7&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-switch.vue?vue&type=style&index=0&id=4a8c9de7&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756166577529\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}