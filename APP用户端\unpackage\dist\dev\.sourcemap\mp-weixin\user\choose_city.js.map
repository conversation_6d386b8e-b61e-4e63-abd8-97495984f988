{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/choose_city.vue?582e", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/choose_city.vue?0678", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/choose_city.vue?c070", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/choose_city.vue?d1d6", "uni-app:///user/choose_city.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/choose_city.vue?6374", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/choose_city.vue?e720"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "flag", "city_id", "position", "keyword", "indexList", "itemArr", "pxArr", "searchArr", "hotList", "methods", "searchCity", "handlePosition", "console", "cityname", "uni", "id", "getPositionAgain", "icon", "duration", "title", "getFocus", "url", "getNowPosition", "type", "isHighAccuracy", "accuracy", "success", "that", "getCity", "res", "item", "getHotCity", "chageABC", "arr", "onLoad", "pinyin", "checkPolyphone", "charCase", "onReady"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,aAAa,sUAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrDA;AAAA;AAAA;AAAA;AAA01B,CAAgB,02BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACqD92B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC,2GACA,UACA,wBACA;MACAC,UACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,GACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAA;MAAA;MACA;MACA;MACA;QACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA;gBAAA,OACA;kBACAC;kBACAZ;gBACA;cAAA;gBACAa;kBACAC;kBACAb;gBACA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAc;MACAF;QACAG;QACAC;QACAC;MACA;MACA;IACA;IACAC;MAAA;MACAN;QACAO;MACA;IACA;IACAC;MAAA;MACA;MACAR;QACAS;QACAC;QACAC;QACAC;UACAZ;YACAO;YACAK;cACA;cACAC,oEACA,4DACA5B;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA;UACA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA6B;MAAA;MACA;QACAC;UACAC;YACA;UACA;QACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACAC;QACA;QACA;UAAA;QAAA;QACA;MACA;MACA;IACA;EAEA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA1B;EACA,EACA;EACA;EACA;EACA;EACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxPA;AAAA;AAAA;AAAA;AAAimD,CAAgB,qjDAAG,EAAC,C;;;;;;;;;;;ACArnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/choose_city.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/choose_city.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./choose_city.vue?vue&type=template&id=20f62b46&scoped=true&\"\nvar renderjs\nimport script from \"./choose_city.vue?vue&type=script&lang=js&\"\nexport * from \"./choose_city.vue?vue&type=script&lang=js&\"\nimport style0 from \"./choose_city.vue?vue&type=style&index=0&id=20f62b46&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"20f62b46\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/choose_city.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./choose_city.vue?vue&type=template&id=20f62b46&scoped=true&\"", "var components\ntry {\n  components = {\n    uSearch: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-search/u-search\" */ \"uview-ui/components/u-search/u-search.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uIndexList: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-index-list/u-index-list\" */ \"uview-ui/components/u-index-list/u-index-list.vue\"\n      )\n    },\n    uIndexItem: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-index-item/u-index-item\" */ \"uview-ui/components/u-index-item/u-index-item.vue\"\n      )\n    },\n    uIndexAnchor: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-index-anchor/u-index-anchor\" */ \"uview-ui/components/u-index-anchor/u-index-anchor.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./choose_city.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./choose_city.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\">\r\n\t\t<view class=\"header\">\r\n\t\t\t<u-search placeholder=\"请输入城市名称\" v-model=\"keyword\" :show-action=\"false\" @input=\"searchCity\"></u-search>\r\n\t\t\t<view class=\"nowP\">\r\n\t\t\t\t<view class=\"left\">\r\n\t\t\t\t\t<u-icon name=\"map-fill\"></u-icon>\r\n\t\t\t\t\t<text style=\"margin-left: 16rpx;margin-right: 12rpx;\">{{position}}</text>\r\n\t\t\t\t\t<span>当前定位</span>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"right\" @tap=\"getPositionAgain\">\r\n\t\t\t\t\t重新定位\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"main\">\r\n\t\t\t<u-index-list :index-list=\"indexList\" v-if=\"keyword == '' && flag\">\r\n\t\t\t\t<!-- <view class=\"head\">\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"hot\">\r\n\t\t\t\t\t\t<text>热门城市</text>\r\n\t\t\t\t\t\t<view class=\"hot-box\">\r\n\t\t\t\t\t\t\t<view class=\"box-item\" v-for=\"(item,index) in hotList\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t{{item.cityname}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<template v-for=\"(item, index) in itemArr\" >\r\n\t\t\t\t\t<!-- #ifdef APP-NVUE -->\r\n\t\t\t\t\t<u-index-anchor :text=\"indexList[index]\"></u-index-anchor>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<u-index-item>\r\n\t\t\t\t\t\t<!-- #ifndef APP-NVUE -->\r\n\t\t\t\t\t\t<u-index-anchor :text=\"indexList[index]\"></u-index-anchor>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t<view class=\"list-cell\" v-for=\"(cell, j) in item\" :key=\"j\" @click=\"handlePosition(cell)\">\r\n\t\t\t\t\t\t\t{{cell.true_name}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</u-index-item>\r\n\t\t\t\t</template>\r\n\t\t\t</u-index-list>\r\n\t\t\t<view class=\"city_box\" v-else>\r\n\t\t\t\t<view class=\"box_item\" v-for=\"(item,index) in searchArr\" :key=\"index\"\r\n\t\t\t\t\tstyle=\"height: 100rpx;line-height: 100rpx;padding: 0 30rpx;\" @click=\"handlePosition(item)\">\r\n\t\t\t\t\t{{item.true_name}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport pinyin from 'js-pinyin'\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tflag: false,\r\n\t\t\t\tcity_id: '',\r\n\t\t\t\tposition: '',\r\n\t\t\t\tkeyword: '',\r\n\t\t\t\tindexList: [\"A\", \"B\", \"C\", \"D\", \"E\", \"F\", \"G\", \"H\", \"I\", \"J\", \"K\", \"L\", \"M\", \"N\", \"O\", \"P\", \"Q\", \"R\", \"S\",\r\n\t\t\t\t\t\"T\", \"U\",\r\n\t\t\t\t\t\"V\", \"W\", \"X\", \"Y\", \"Z\"\r\n\t\t\t\t],\r\n\t\t\t\titemArr: [\r\n\t\t\t\t\t[],\r\n\t\t\t\t\t[],\r\n\t\t\t\t\t[],\r\n\t\t\t\t\t[],\r\n\t\t\t\t\t[],\r\n\t\t\t\t\t[],\r\n\t\t\t\t\t[],\r\n\t\t\t\t\t[],\r\n\t\t\t\t\t[],\r\n\t\t\t\t\t[],\r\n\t\t\t\t\t[],\r\n\t\t\t\t\t[],\r\n\t\t\t\t\t[],\r\n\t\t\t\t\t[],\r\n\t\t\t\t\t[],\r\n\t\t\t\t\t[],\r\n\t\t\t\t\t[],\r\n\t\t\t\t\t[],\r\n\t\t\t\t\t[],\r\n\t\t\t\t\t[],\r\n\t\t\t\t\t[],\r\n\t\t\t\t\t[],\r\n\t\t\t\t\t[],\r\n\t\t\t\t\t[],\r\n\t\t\t\t\t[],\r\n\t\t\t\t\t[]\r\n\t\t\t\t],\r\n\t\t\t\tpxArr: [],\r\n\t\t\t\tsearchArr: [],\r\n\t\t\t\thotList: []\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tsearchCity() { //搜索城市\r\n\t\t\t\tthis.searchArr = []\r\n\t\t\t\tif (this.keyword == '') return\r\n\t\t\t\tthis.pxArr.forEach(item => {\r\n\t\t\t\t\tif (item.true_name.includes(this.keyword)) {\r\n\t\t\t\t\t\tthis.searchArr.push(item)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tasync handlePosition(e) {\r\n\t\t\t\tconsole.log(e)\r\n\t\t\t\tawait this.$api.service.selectCity({\r\n\t\t\t\t\tcityname: e.true_name,\r\n\t\t\t\t\tcity_id: e.id\r\n\t\t\t\t})\r\n\t\t\t\tuni.setStorageSync('city', {\r\n\t\t\t\t\tid: e.id,\r\n\t\t\t\t\tposition: e.true_name\r\n\t\t\t\t});\r\n\t\t\t\tthis.position = e.true_name\r\n\t\t\t\tthis.city_id = e.id\r\n\t\t\t\tthis.keyword = ''\r\n\t\t\t},\r\n\t\t\tgetPositionAgain() {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: 'loading',\r\n\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\ttitle: '定位中'\r\n\t\t\t\t})\r\n\t\t\t\tthis.getNowPosition()\r\n\t\t\t},\r\n\t\t\tgetFocus() { //输入框获得焦点\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/city_search/city_search'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetNowPosition() { //当前位置\r\n\t\t\t\tlet that = this\r\n\t\t\t\tuni.getLocation({\r\n\t\t\t\t\ttype: 'gcj02',\r\n\t\t\t\t\tisHighAccuracy: true,\r\n\t\t\t\t\taccuracy: 'best',\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tuni.request({\r\n\t\t\t\t\t\t\turl: `https://restapi.amap.com/v3/geocode/regeo?key=4272f5716dfd17882409f306c0299666&location=${res.longitude},${res.latitude}`,\r\n\t\t\t\t\t\t\tsuccess: function(res1) {\r\n\t\t\t\t\t\t\t\tlet province = res1.data.regeocode.addressComponent.province\r\n\t\t\t\t\t\t\t\tthat.position = typeof res1.data.regeocode.addressComponent.city ==\r\n\t\t\t\t\t\t\t\t\t'string' ? res1.data.regeocode.addressComponent.city : res1\r\n\t\t\t\t\t\t\t\t\t.data.regeocode.addressComponent.province\r\n\t\t\t\t\t\t\t\t// that.$api.service.getCity(0).then(res2 => {\r\n\t\t\t\t\t\t\t\t// \tlet index = res2.findIndex(item => item.true_name ==\r\n\t\t\t\t\t\t\t\t// \t\tprovince)\r\n\t\t\t\t\t\t\t\t// \tlet province_id = res2[index].id\r\n\t\t\t\t\t\t\t\t// \tthat.$api.service.getCity(province_id).then(res3 => {\r\n\t\t\t\t\t\t\t\t// \t\tlet index2 = res3.findIndex(e => e\r\n\t\t\t\t\t\t\t\t// \t\t\t.true_name == that.position)\r\n\t\t\t\t\t\t\t\t// \t\tthat.city_id = [res3[index2].id]\r\n\t\t\t\t\t\t\t\t// \t\tthat.$api.service.index(that.city_id).then(\r\n\t\t\t\t\t\t\t\t// \t\t\tres4 => {\r\n\t\t\t\t\t\t\t\t// \t\t\t\tthat.bannerList = res4.banner\r\n\t\t\t\t\t\t\t\t// \t\t\t\tthat.list1 = res4.banner.map(\r\n\t\t\t\t\t\t\t\t// \t\t\t\t\titem => {\r\n\t\t\t\t\t\t\t\t// \t\t\t\t\t\treturn item.img\r\n\t\t\t\t\t\t\t\t// \t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t// \t\t\t\tthat.baseList = res4.jingang\r\n\t\t\t\t\t\t\t\t// \t\t\t\tthat.text1 = res4.notice.map(\r\n\t\t\t\t\t\t\t\t// \t\t\t\t\titem => {\r\n\t\t\t\t\t\t\t\t// \t\t\t\t\t\treturn item.content\r\n\t\t\t\t\t\t\t\t// \t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t// \t\t\t\tthat.service = res4\r\n\t\t\t\t\t\t\t\t// \t\t\t\t\t.service_cate\r\n\t\t\t\t\t\t\t\t// \t\t\t})\r\n\t\t\t\t\t\t\t\t// \t})\r\n\t\t\t\t\t\t\t\t// })\r\n\t\t\t\t\t\t\t\t// let data = {\r\n\t\t\t\t\t\t\t\t// \tcity: that.position,\r\n\t\t\t\t\t\t\t\t// \tlng: res.longitude,\r\n\t\t\t\t\t\t\t\t// \tlat: res.latitude\r\n\t\t\t\t\t\t\t\t// }\r\n\t\t\t\t\t\t\t\t// uni.setStorageSync('city',{city_id:that.city_id,position:that.position})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// getAllCity() {\r\n\t\t\t// \tuni.request({\r\n\t\t\t// \t\turl: 'https://restapi.amap.com/v3/config/district?keywords=中国&subdistrict=2&key=4272f5716dfd17882409f306c0299666',\r\n\t\t\t// \t\tsuccess: (res) => {\r\n\t\t\t// \t\t\tres.data.districts[0].districts.forEach(item => {\r\n\t\t\t// \t\t\t\titem.districts.forEach(e => {\r\n\t\t\t// \t\t\t\t\tthis.pxArr.push(e.name)\r\n\t\t\t// \t\t\t\t})\r\n\t\t\t// \t\t\t})\r\n\t\t\t// \t\t\tthis.chageABC(this.pxArr)\r\n\t\t\t// \t\t}\r\n\t\t\t// \t})\r\n\t\t\t// },\r\n\t\t\tgetCity() {\r\n\t\t\t\tthis.$api.service.allCity().then(res => {\r\n\t\t\t\t\tres.forEach(item => {\r\n\t\t\t\t\t\titem.children.forEach(e => {\r\n\t\t\t\t\t\t\tthis.pxArr.push(e)\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.chageABC(this.pxArr)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetHotCity() {\r\n\t\t\t\tthis.$api.service.hotCity().then(res => {\r\n\t\t\t\t\tif (res.length > 9) {\r\n\t\t\t\t\t\tthis.hotList = res.slice(0, 9)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.hotList = res\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tchageABC(arr) {\r\n\t\t\t\tarr.map(item => {\r\n\t\t\t\t\tconst key = pinyin.getFullChars(item.true_name).charAt(0)\r\n\t\t\t\t\tlet index = this.indexList.findIndex(e => e == key)\r\n\t\t\t\t\tthis.itemArr[index].push(item)\r\n\t\t\t\t})\r\n\t\t\t\tthis.flag = true\r\n\t\t\t}\r\n\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\t// this.city_id = uni.getStorageSync('city').id;\r\n\t\t\t// this.position = uni.getStorageSync('city').position\r\n\t\t\tthis.getCity()\r\n\t\t\t// this.getAllCity()\r\n\t\t\tthis.getHotCity()\r\n\t\t\t// this.getPosition()\r\n\t\t\tpinyin.setOptions({\r\n\t\t\t\tcheckPolyphone: false,\r\n\t\t\t\tcharCase: 0\r\n\t\t\t});\r\n\t\t},\r\n\t\tonReady() {\r\n\t\t\tconsole.log(this.itemArr, this.indexList);\r\n\t\t},\r\n\t\t// onUnload() {\r\n\t\t// \tuni.$emit('confirmPosition', {\r\n\t\t// \t\tposition: this.position,\r\n\t\t// \t\tcity_id: this.city_id\r\n\t\t// \t});\r\n\t\t// },\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.page {\r\n\t\twidth: 100%;\r\n\r\n\t\t.header {\r\n\t\t\tpadding: 24rpx 30rpx;\r\n\t\t\tbackground-color: #fff;\r\n\r\n\t\t\t.nowP {\r\n\t\t\t\theight: 40rpx;\r\n\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t.left {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t\tspan {\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.right {\r\n\t\t\t\t\tcolor: #2E80FE;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.main {\r\n\t\t\t/deep/ .u-index-list__letter {\r\n\t\t\t\ttop: 140px !important;\r\n\t\t\t}\r\n\r\n\r\n\r\n\t\t\t.head {\r\n\t\t\t\theight: 400rpx;\r\n\t\t\t\tpadding: 20rpx 30rpx;\r\n\t\t\t\tbackground-color: #F8F8F8;\r\n\r\n\t\t\t\t.last {\r\n\t\t\t\t\theight: 140rpx;\r\n\r\n\t\t\t\t\ttext {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.last-box {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tflex-wrap: wrap;\r\n\r\n\t\t\t\t\t\t.box-item {\r\n\t\t\t\t\t\t\twidth: fit-content;\r\n\t\t\t\t\t\t\tmin-width: 210rpx;\r\n\t\t\t\t\t\t\theight: 68rpx;\r\n\t\t\t\t\t\t\tmargin: 20rpx 7rpx;\r\n\t\t\t\t\t\t\tbackground: #FFFFFF;\r\n\t\t\t\t\t\t\tline-height: 68rpx;\r\n\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.hot {\r\n\t\t\t\t\theight: 260rpx;\r\n\r\n\t\t\t\t\ttext {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.hot-box {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tflex-wrap: wrap;\r\n\r\n\t\t\t\t\t\t.box-item {\r\n\t\t\t\t\t\t\tpadding: 0 10rpx;\r\n\t\t\t\t\t\t\twidth: fit-content;\r\n\t\t\t\t\t\t\tmin-width: 210rpx;\r\n\t\t\t\t\t\t\tmargin: 20rpx 7rpx;\r\n\t\t\t\t\t\t\tbackground: #FFFFFF;\r\n\t\t\t\t\t\t\tline-height: 68rpx;\r\n\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\r\n\t\t\t.list-cell {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tpadding: 10px 24rpx;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\tcolor: #323233;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\tline-height: 24px;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./choose_city.vue?vue&type=style&index=0&id=20f62b46&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./choose_city.vue?vue&type=style&index=0&id=20f62b46&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756166578312\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}