{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/cart_play.vue?c80a", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/cart_play.vue?a51e", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/cart_play.vue?d0f2", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/cart_play.vue?7beb", "uni-app:///user/cart_play.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/cart_play.vue?7f83", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/cart_play.vue?dcc3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "cartItems", "serviceMap", "m<PERSON><PERSON><PERSON><PERSON>", "notes", "isSubmitting", "showChoose", "content", "currentDate", "baseTimeArr", "disabled", "time", "time1", "time2", "displayedTimeArr", "currentTime", "conDate", "conTime", "dateArr", "week", "computed", "groupedCartItems", "groups", "serviceId", "items", "urgent", "totalAmount", "total", "methods", "getServiceImage", "getServiceTitle", "getServicePrice", "updateQuantity", "console", "originalNum", "index", "num", "id", "res", "uni", "icon", "title", "duration", "getServicesInfo", "serviceIds", "getDefaultAddress", "goUrl", "url", "success", "fail", "getTime", "str", "date", "fullDate", "addLeadingZero", "updateTimeAvailability", "tapDate", "tapTime", "confirmTime", "submitOrder", "cartOrderings", "addressId", "carIds", "endTime", "startTime", "text", "type", "setTimeout", "toggle<PERSON><PERSON>", "toggleGroupUrgent", "onLoad", "ids", "options", "onShow", "that"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9HA;AAAA;AAAA;AAAA;AAAw1B,CAAgB,w2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCsH52B;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;;MAEA;MACAC;MACAC;MACAC;MACA;MACAC,cACA;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,EACA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;;MAEA;MACA;QACA;UACAC;YACAC;YACAC;YACAC;UACA;QACA;;QACAH;MACA;;MAEA;MACA;IACA;IAEA;IACAI;MAAA;MACA;MACA;QACA;QACAC;MACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACAC;gBAEA;gBACAC;kBAAA;gBAAA;gBACA;kBACA;oBAAAC;kBAAA;gBACA;gBAAA;gBAAA;gBAAA,OAGA;kBACAb;kBACAc;kBACAD;gBACA;cAAA;gBAJAE;gBAMA;kBACAC;oBACAC;oBACAC;oBACAC;kBACA;gBACA;kBACA;kBACA;oBACA;sBAAAN;oBAAA;kBACA;kBACAG;oBACAC;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAT;gBACA;gBACA;kBACA;oBAAAG;kBAAA;gBACA;gBACAG;kBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAKA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,uCAEAC;gBAAA;gBAAA;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAArB;gBAAA;gBAAA,OACA;cAAA;gBAAAe;gBACA;kBACA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAL;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAY;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAP;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAL;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAa;MACA;MAEAb;MACAM;QACAQ;QACAC;UACAf;QACA;QACAgB;UACAhB;UACAM;YACAC;YACAC;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAQ;MACA;MACA;MAEA;QACA;QACA;QACA;QACA;QAEA;UACAC;UACAC;UACAC;QACA;QAEA7C;MACA;MAEA;IACA;IAEA;IACA8C;MACA;IACA;IAEA;IACAC;MAAA;MACAtB;MACA;MACA;MACA;;MAEA;MACA;MAEA;QAAA;QACA;UACA;YACAA;YACA;UACA;;UACA;UACA;;UAEA;UACA;YACA;UAAA,CACA;YACA;YACA;cAAAvB;YAAA;UACA;QACA;MACA;QACA;QACA;UAAA;YAAAA;UAAA;QAAA;MACA;MACAuB;IACA;IAEA;IACAuB;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACAlB;UACAC;UACAC;UACAC;QACA;QACA;MACA;MACA;MACA;IACA;IAEA;IACAgB;MACA;QACAnB;UACAC;UACAC;UACAC;QACA;QACA;MACA;MACA;MACA;QACAH;UACAC;UACAC;UACAC;QACA;QACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAiB;MAAA;MACA;MAEA;QACApB;UACAC;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAH;UACAC;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAH;UACAC;UACAC;UACAC;QACA;QACA;MACA;MAEA;;MAEA;MACA;MACA;MAEA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;QACAkB;UACAC;UACAC;YAAA;UAAA;UAAA;UACAC;UACAxC;UACAyC;UACAC;UAAA;UACAC;UAAA;UACAzC;QACA;MACA;;MAEA;MACA;QACAmC;MACA;MAEA3B;;MAEA;MACA;QACA;UACAM;YACAC;YACAC;YACAC;UACA;UAEAyB;YACA;YACA;YACA5B;cACAQ;YACA;UACA;QACA;UACA;UACAR;YACAC;YACAC;YACAC;UACA;QACA;MACA;QACAT;QACA;QACAM;UACAC;UACAC;UACAC;QACA;MACA;IACA;IACA;IACA0B;MACA;MACA;QACA;MACA;MACA;MACA;MACAnC;IACA;IACA;IACAoC;MACA;MACA;MACApC;IACA;EACA;EACAqC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACArC;cACAsC;cAAA,KACAC;gBAAA;gBAAA;cAAA;cAAA;cAEAD;cAAA;cAAA,OACA;gBAAAA;cAAA;YAAA;cAAAjC;cACAL;;cAEA;cAAA,MACAK;gBAAA;gBAAA;cAAA;cACA;;cAEA;cACAM;gBAAA;cAAA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA;YAAA;cAAA;cAAA;cAGAX;YAAA;cAAA;cAAA;YAAA;cAGAA;YAAA;cAGA;cACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EAEAwC;IACA;IACAlC;MACAmC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjjBA;AAAA;AAAA;AAAA;AAA+lD,CAAgB,mjDAAG,EAAC,C;;;;;;;;;;;ACAnnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/cart_play.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/cart_play.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./cart_play.vue?vue&type=template&id=a95b2d86&scoped=true&\"\nvar renderjs\nimport script from \"./cart_play.vue?vue&type=script&lang=js&\"\nexport * from \"./cart_play.vue?vue&type=script&lang=js&\"\nimport style0 from \"./cart_play.vue?vue&type=style&index=0&id=a95b2d86&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"a95b2d86\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/cart_play.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cart_play.vue?vue&type=template&id=a95b2d86&scoped=true&\"", "var components\ntry {\n  components = {\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-modal/u-modal\" */ \"uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uNumberBox: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-number-box/u-number-box\" */ \"uview-ui/components/u-number-box/u-number-box.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.displayedTimeArr.slice(0, 6)\n  var l1 = _vm.displayedTimeArr.slice(6)\n  var l4 = _vm.__map(_vm.groupedCartItems, function (group, groupIndex) {\n    var $orig = _vm.__get_orig(group)\n    var g0 = group.items.length\n    var m0 = g0 > 0 ? _vm.getServiceTitle(group.items[0]) : null\n    var l3 = _vm.__map(group.items, function (item, index) {\n      var $orig = _vm.__get_orig(item)\n      var m1 = _vm.getServiceImage(item)\n      var m2 = _vm.getServiceTitle(item)\n      var g1 = item.list && item.list.length > 1\n      var l2 = g1\n        ? _vm.__map(item.list.slice(0, -1), function (setting, idx) {\n            var $orig = _vm.__get_orig(setting)\n            var g2 = item.list.length\n            return {\n              $orig: $orig,\n              g2: g2,\n            }\n          })\n        : null\n      var m3 = _vm.getServicePrice(item)\n      return {\n        $orig: $orig,\n        m1: m1,\n        m2: m2,\n        g1: g1,\n        l2: l2,\n        m3: m3,\n      }\n    })\n    return {\n      $orig: $orig,\n      g0: g0,\n      m0: m0,\n      l3: l3,\n    }\n  })\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showChoose = false\n    }\n    _vm.e1 = function ($event, item, index) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        item = _temp2.item,\n        index = _temp2.index\n      var _temp, _temp2\n      return _vm.tapTime(item, index)\n    }\n    _vm.e2 = function ($event, item, index) {\n      var _temp3 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp4 = _temp3.eventParams || _temp3[\"event-params\"],\n        item = _temp4.item,\n        index = _temp4.index\n      var _temp3, _temp4\n      return _vm.tapTime(item, index + 6)\n    }\n    _vm.e3 = function ($event) {\n      _vm.showChoose = true\n    }\n    _vm.e4 = function (e, item) {\n      var args = [],\n        len = arguments.length - 2\n      while (len-- > 0) args[len] = arguments[len + 2]\n\n      var _temp5 = args[args.length - 1].currentTarget.dataset,\n        _temp6 = _temp5.eventParams || _temp5[\"event-params\"],\n        item = _temp6.item\n      var _temp5, _temp6\n      return _vm.updateQuantity(item, e.value)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        l1: l1,\n        l4: l4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cart_play.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cart_play.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<u-modal :show=\"showChoose\" :content='content'></u-modal>\n\n\t\t<view class=\"choose_time\" :style=\"showChoose?'':'height:0'\">\n\t\t\t<view class=\"head\">请选择时间</view>\n\t\t\t<view class=\"close\" @click=\"showChoose = false\">\n\t\t\t\t<image src=\"../static/images/9397.png\" mode=\"\"></image>\n\t\t\t</view>\n\t\t\t<view class=\"date\">\n\t\t\t\t<view class=\"date_item\" v-for=\"(item,index) in dateArr\" :key=\"index\"\n\t\t\t\t\t:style=\"currentDate == index?'color:#2E80FE;':''\" @tap=\"tapDate(item,index)\">\n\t\t\t\t\t<view class=\"\">{{item.str}}</view>\n\t\t\t\t\t<view class=\"\">{{item.date}}</view>\n\t\t\t\t\t<view class=\"hk\" :style=\"currentDate == index?'':'display:none;'\"></view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<scroll-view scroll-y=\"true\" class=\"time_all\">\n\t\t\t\t<view class=\"time_columns\">\n\t\t\t\t\t<view class=\"time_column\">\n\t\t\t\t\t\t<view class=\"time_item\" v-for=\"(item, index) in displayedTimeArr.slice(0, 6)\" :key=\"index\"\n\t\t\t\t\t\t\tv-if=\"item.time && item.time1 && item.time2\"\n\t\t\t\t\t\t\t:style=\"item.disabled ? 'background-color:#adadad;color:#fff;' : currentTime === index ? 'background-color:#2E80FE;color:#fff;' : ''\"\n\t\t\t\t\t\t\t@tap=\"tapTime(item, index)\">\n\t\t\t\t\t\t\t{{item.time}}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"time_column\">\n\t\t\t\t\t\t<view class=\"time_item\" v-for=\"(item, index) in displayedTimeArr.slice(6)\" :key=\"index\"\n\t\t\t\t\t\t\tv-if=\"item.time && item.time1 && item.time2\"\n\t\t\t\t\t\t\t:style=\"item.disabled ? 'background-color:#adadad;color:#fff;' : currentTime === index + 6 ? 'background-color:#2E80FE;color:#fff;' : ''\"\n\t\t\t\t\t\t\t@tap=\"tapTime(item, index + 6)\">\n\t\t\t\t\t\t\t{{item.time}}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t\t<view class=\"btn\" @tap=\"confirmTime\">确定预约时间</view>\n\t\t</view>\n\n\t\t<view class=\"address\" @click=\"goUrl\">\n\t\t\t<view class=\"left\">\n\t\t\t\t<view class=\"top\">\n\t\t\t\t\t<image src=\"../static/images/position.png\" mode=\"\"></image>\n\t\t\t\t\t<text style=\"color: #599eff;\">{{mrAddress.address?mrAddress.address:'请先添加地址哦'}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"bottom\">{{mrAddress.address?mrAddress.userName+mrAddress.mobile:''}}</view>\n\t\t\t</view>\n\t\t\t<u-icon name=\"arrow-right\" color=\"#333333\" size=\"14\"></u-icon>\n\t\t</view>\n\n\t\t<view class=\"time\" @click=\"showChoose = true\">\n\t\t\t<view class=\"left\">\n\t\t\t\t<image src=\"../static/images/clock.png\" mode=\"\"></image>\n\t\t\t\t<text>{{conDate + (conTime ? ' ' + conTime : '')}}</text>\n\t\t\t</view>\n\t\t\t<u-icon name=\"arrow-right\" color=\"#333333\" size=\"14\"></u-icon>\n\t\t</view>\n\n\t\t<view class=\"fg\"></view>\n\n\t\t<view class=\"main\">\n\t\t\t<block v-for=\"(group, groupIndex) in groupedCartItems\" :key=\"groupIndex\">\n\t\t\t\t<!-- 如果不是第一组，添加分隔线 -->\n\t\t\t\t<view class=\"service-divider\" v-if=\"groupIndex > 0\"></view>\n\t\t\t\t\n\t\t\t\t<!-- 分组标题和加急选项 -->\n\t\t\t\t<view class=\"group-header\" v-if=\"group.items.length > 0\">\n\t\t\t\t\t<view class=\"group-title\">{{getServiceTitle(group.items[0])}}</view>\n\t\t\t\t\t<view class=\"urgent-option\">\n\t\t\t\t\t\t<checkbox \n\t\t\t\t\t\t\t:checked=\"group.urgent === 1\" \n\t\t\t\t\t\t\t@click=\"toggleGroupUrgent(group)\"\n\t\t\t\t\t\t\tstyle=\"transform: scale(0.7);\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t\t<text class=\"urgent-text\">加急</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 显示该组的商品 -->\n\t\t\t\t<view class=\"service-items\">\n\t\t\t\t\t<view class=\"main_item\" v-for=\"(item, index) in group.items\" :key=\"index\">\n\t\t\t\t\t\t<image :src=\"getServiceImage(item)\" mode=\"\"></image>\n\t\t\t\t\t\t<view class=\"right\">\n\t\t\t\t\t\t\t<view class=\"title\">{{getServiceTitle(item)}}</view>\n\t\t\t\t\t\t\t<!-- 修改已选项显示，排除最后一个值 -->\n\t\t\t\t\t\t\t<view class=\"selected-options\" v-if=\"item.list && item.list.length > 1\">\n\t\t\t\t\t\t\t\t<text class=\"selected-label\">已选：</text>\n\t\t\t\t\t\t\t\t<text class=\"selected-value\" v-for=\"(setting, idx) in item.list.slice(0, -1)\" :key=\"idx\">\n\t\t\t\t\t\t\t\t\t{{ setting.val }}<text v-if=\"idx !== item.list.length - 2\">,</text>\n\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"price\">\n\t\t\t\t\t\t\t\t<text>￥{{getServicePrice(item)}}/台</text>\n\t\t\t\t\t\t\t\t<u-number-box v-model=\"item.num\" :min=\"1\" @change=\"e => updateQuantity(item, e.value)\"></u-number-box>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</block>\n\t\t</view>\n\n\t\t<view class=\"fg\"></view>\n\n\t\t\n\t\t<view class=\"fg\"></view>\n\n\t\t<view class=\"footer\">\n\t\t\t<view class=\"left\">总计￥{{totalAmount}}</view>\n\t\t\n\t\t\t<view class=\"right\" :class=\"{'disabled': isSubmitting}\" @click=\"submitOrder\">\n\t\t\t\t{{isSubmitting ? '提交中...' : '立即下单'}}\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tcartItems: [], // 购物车商品列表\n\t\t\t\tserviceMap: {}, // 服务信息映射\n\t\t\t\tmrAddress: {}, // 默认地址\n\t\t\t\tnotes: '', // 服务备注\n\t\t\t\tisSubmitting: false, // 提交状态\n\n\t\t\t\t// 时间选择相关\n\t\t\t\tshowChoose: false,\n\t\t\t\tcontent: '',\n\t\t\t\tcurrentDate: 0,\n\t\t\t\t// Base time array - always contains all time slots\n\t\t\t\tbaseTimeArr: [\n\t\t\t\t\t{ disabled: false, time: '00:00-02:00', time1: '00:00:00', time2: '02:00:00' },\n\t\t\t\t\t{ disabled: false, time: '02:00-04:00', time1: '02:00:00', time2: '04:00:00' },\n\t\t\t\t\t{ disabled: false, time: '04:00-06:00', time1: '04:00:00', time2: '06:00:00' },\n\t\t\t\t\t{ disabled: false, time: '06:00-08:00', time1: '06:00:00', time2: '08:00:00' },\n\t\t\t\t\t{ disabled: false, time: '08:00-10:00', time1: '08:00:00', time2: '10:00:00' },\n\t\t\t\t\t{ disabled: false, time: '10:00-12:00', time1: '10:00:00', time2: '12:00:00' },\n\t\t\t\t\t{ disabled: false, time: '12:00-14:00', time1: '12:00:00', time2: '14:00:00' },\n\t\t\t\t\t{ disabled: false, time: '14:00-16:00', time1: '14:00:00', time2: '16:00:00' },\n\t\t\t\t\t{ disabled: false, time: '16:00-18:00', time1: '16:00:00', time2: '18:00:00' },\n\t\t\t\t\t{ disabled: false, time: '18:00-20:00', time1: '18:00:00', time2: '20:00:00' },\n\t\t\t\t\t{ disabled: false, time: '20:00-22:00', time1: '20:00:00', time2: '22:00:00' },\n\t\t\t\t\t{ disabled: false, time: '22:00-24:00', time1: '22:00:00', time2: '24:00:00' }\n\t\t\t\t],\n\t\t\t\tdisplayedTimeArr: [], // Time array adjusted based on selected date (today vs future)\n\t\t\t\tcurrentTime: -1,\n\t\t\t\tconDate: '选择可上门时间',\n\t\t\t\tconTime: '',\n\t\t\t\tdateArr: [],\n\t\t\t\tweek: [\"周日\", \"周一\", \"周二\", \"周三\", \"周四\", \"周五\", \"周六\"]\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t// 按serviceId分组的购物车商品\n\t\t\tgroupedCartItems() {\n\t\t\t\t// 创建一个Map来存储分组\n\t\t\t\tconst groups = new Map();\n\t\t\t\t\n\t\t\t\t// 遍历购物车商品进行分组\n\t\t\t\tthis.cartItems.forEach(item => {\n\t\t\t\t\tif (!groups.has(item.serviceId)) {\n\t\t\t\t\t\tgroups.set(item.serviceId, {\n\t\t\t\t\t\t\tserviceId: item.serviceId,\n\t\t\t\t\t\t\titems: [],\n\t\t\t\t\t\t\turgent: 0 // 默认不加急\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\tgroups.get(item.serviceId).items.push(item);\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 将Map转换为数组返回\n\t\t\t\treturn Array.from(groups.values());\n\t\t\t},\n\t\t\t\n\t\t\t// 计算总金额\n\t\t\ttotalAmount() {\n\t\t\t\tlet total = 0;\n\t\t\t\tthis.cartItems.forEach(item => {\n\t\t\t\t\tconst price = this.getServicePrice(item);\n\t\t\t\t\ttotal += price * item.num;\n\t\t\t\t});\n\t\t\t\treturn total.toFixed(2);\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t// 获取服务图片\n\t\t\tgetServiceImage(cartItem) {\n\t\t\t\tconst service = this.serviceMap[cartItem.serviceId];\n\t\t\t\treturn service ? service.cover : '';\n\t\t\t},\n\n\t\t\t// 获取服务标题\n\t\t\tgetServiceTitle(cartItem) {\n\t\t\t\tconst service = this.serviceMap[cartItem.serviceId];\n\t\t\t\treturn service ? service.title : '未知服务';\n\t\t\t},\n\n\t\t\t// 获取服务价格\n\t\t\tgetServicePrice(cartItem) {\n\t\t\t\tconst service = this.serviceMap[cartItem.serviceId];\n\t\t\t\treturn service ? parseFloat(service.price) : 0;\n\t\t\t},\n\n\t\t\t// 更新数量\n\t\t\tasync updateQuantity(item, newNum) {\n\t\t\t\tconsole.log('Updating quantity for item:', item.id, 'New number:', newNum);\n\t\t\t\tconst originalNum = item.num; // Store original number for rollback\n\n\t\t\t\t// Immediately update the local item's num for quick UI feedback\n\t\t\t\tconst index = this.cartItems.findIndex(i => i.id === item.id);\n\t\t\t\tif (index !== -1) {\n\t\t\t\t\tthis.$set(this.cartItems, index, { ...this.cartItems[index], num: newNum });\n\t\t\t\t}\n\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await this.$api.service.updatatocar({\n\t\t\t\t\t\tserviceId: item.serviceId,\n\t\t\t\t\t\tid: item.id,\n\t\t\t\t\t\tnum: newNum\n\t\t\t\t\t});\n\n\t\t\t\t\tif (res.code === \"200\") {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\ttitle: '数量已更新',\n\t\t\t\t\t\t\tduration: 500\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// If API fails, revert to original number\n\t\t\t\t\t\tif (index !== -1) {\n\t\t\t\t\t\t\tthis.$set(this.cartItems, index, { ...this.cartItems[index], num: originalNum });\n\t\t\t\t\t\t}\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: res.msg || '更新失败',\n\t\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} catch (err) {\n\t\t\t\t\tconsole.error('更新数量失败:', err);\n\t\t\t\t\t// If network request fails, revert to original number\n\t\t\t\t\tif (index !== -1) {\n\t\t\t\t\t\tthis.$set(this.cartItems, index, { ...this.cartItems[index], num: originalNum });\n\t\t\t\t\t}\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '网络错误，请重试',\n\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t// Force update is generally not needed if using $set correctly,\n\t\t\t\t// but can be a fallback for complex reactivity issues if they persist.\n\t\t\t\t// this.$forceUpdate();\n\t\t\t},\n\n\t\t\t// 获取服务详细信息\n\t\t\tasync getServicesInfo(serviceIds) {\n\t\t\t\ttry {\n\t\t\t\t\tfor (let serviceId of serviceIds) {\n\t\t\t\t\t\tconst res = await this.$api.service.getserviceInfo(serviceId);\n\t\t\t\t\t\tif (res.data) {\n\t\t\t\t\t\t\tthis.$set(this.serviceMap, serviceId, res.data); // Use $set for reactive addition\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} catch (err) {\n\t\t\t\t\tconsole.error('获取服务信息失败:', err);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 获取默认地址\n\t\t\tasync getDefaultAddress() {\n\t\t\t\ttry {\n\t\t\t\t\tlet res = await this.$api.service.getaddressDefault();\n\t\t\t\t\tthis.mrAddress = res.data;\n\t\t\t\t} catch (err) {\n\t\t\t\t\tconsole.error('获取默认地址失败:', err);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 跳转到地址页面\n\t\t\tgoUrl() {\n\t\t\t\tif (this.isSubmitting) return;\n\n\t\t\t\tconsole.log('goUrl triggered');\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '../user/address',\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tconsole.log('Navigation to address page successful');\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('Navigation failed:', err);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: '导航失败，请检查页面路径',\n\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 初始化时间数据\n\t\t\tgetTime() {\n\t\t\t\tconst now = new Date();\n\t\t\t\tlet currentDate = new Date(now);\n\n\t\t\t\tfor (let i = 0; i < 4; i++) {\n\t\t\t\t\tconst month = this.addLeadingZero(currentDate.getMonth() + 1);\n\t\t\t\t\tconst date = this.addLeadingZero(currentDate.getDate());\n\t\t\t\t\tconst day = currentDate.getDay();\n\t\t\t\t\tconst year = currentDate.getFullYear();\n\n\t\t\t\t\tthis.dateArr.push({\n\t\t\t\t\t\tstr: i === 0 ? '今天' : this.week[day],\n\t\t\t\t\t\tdate: month + '-' + date,\n\t\t\t\t\t\tfullDate: `${year}-${month}-${date}`\n\t\t\t\t\t});\n\n\t\t\t\t\tcurrentDate.setDate(currentDate.getDate() + 1);\n\t\t\t\t}\n\n\t\t\t\tthis.updateTimeAvailability(0);\n\t\t\t},\n\n\t\t\t// 添加前导零\n\t\t\taddLeadingZero(number) {\n\t\t\t\treturn number < 10 ? '0' + number : number;\n\t\t\t},\n\n\t\t\t// 更新时间可用性\n\t\t\tupdateTimeAvailability(dateIndex) {\n\t\t\t\tconsole.log('Updating time availability for dateIndex:', dateIndex);\n\t\t\t\tconst now = new Date();\n\t\t\t\tconst currentHour = now.getHours();\n\t\t\t\tconst currentMinute = now.getMinutes();\n\n\t\t\t\t// Reset displayed time array\n\t\t\t\tthis.displayedTimeArr = [];\n\n\t\t\t\tif (dateIndex === 0) { // Today\n\t\t\t\t\tthis.baseTimeArr.forEach(item => {\n\t\t\t\t\t\tif (!item.time1) {\n\t\t\t\t\t\t\tconsole.warn(`Invalid time slot:`, item);\n\t\t\t\t\t\t\treturn; // Skip invalid items\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconst timeStartHour = parseInt(item.time1.split(':')[0]);\n\t\t\t\t\t\tconst timeStartMinute = parseInt(item.time1.split(':')[1]);\n\n\t\t\t\t\t\t// Check if the time slot has already passed\n\t\t\t\t\t\tif (currentHour > timeStartHour || (currentHour === timeStartHour && currentMinute >= timeStartMinute)) {\n\t\t\t\t\t\t\t// Past time, do not add to displayedTimeArr\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// Future time, add to displayedTimeArr and ensure it's not disabled\n\t\t\t\t\t\t\tthis.displayedTimeArr.push({ ...item, disabled: false });\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\t// Other dates, all time slots are available\n\t\t\t\t\tthis.displayedTimeArr = this.baseTimeArr.map(item => ({ ...item, disabled: false }));\n\t\t\t\t}\n\t\t\t\tconsole.log('Updated displayedTimeArr:', this.displayedTimeArr);\n\t\t\t},\n\n\t\t\t// 选择日期\n\t\t\ttapDate(item, index) {\n\t\t\t\tthis.currentDate = index;\n\t\t\t\tthis.currentTime = -1; // Reset time selection\n\t\t\t\tthis.conTime = ''; // Reset time display\n\t\t\t\tthis.updateTimeAvailability(index);\n\t\t\t},\n\n\t\t\t// 选择时间\n\t\t\ttapTime(item, index) {\n\t\t\t\tif (!item || !item.time || item.disabled) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '该时间段不可选择',\n\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t// Single selection logic: when selecting a new time slot, deselect others\n\t\t\t\tthis.currentTime = index;\n\t\t\t},\n\n\t\t\t// 确认时间\n\t\t\tconfirmTime() {\n\t\t\t\tif (this.currentTime === -1) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '请选择预约时间',\n\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tconst selectedTime = this.displayedTimeArr[this.currentTime];\n\t\t\t\tif (selectedTime.disabled) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '该时间段不可用',\n\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthis.conDate = this.dateArr[this.currentDate].date + '(' + this.dateArr[this.currentDate].str + ')';\n\t\t\t\tthis.conTime = selectedTime.time;\n\t\t\t\tthis.showChoose = false;\n\t\t\t},\n\t\t\n\t\t\t// 提交订单\n\t\t\tsubmitOrder() {\n\t\t\t\tif (this.isSubmitting) return;\n\n\t\t\t\tif (this.conDate == '选择可上门时间') {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '请选择时间',\n\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (!this.mrAddress.id) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '请先选择地址',\n\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (this.currentTime === -1) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '请选择具体时间段',\n\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tthis.isSubmitting = true;\n\n\t\t\t\t// 构建时间字符串\n\t\t\t\tconst selectedDateObj = this.dateArr[this.currentDate];\n\t\t\t\tconst selectedTimeObj = this.displayedTimeArr[this.currentTime];\n\n\t\t\t\tlet dateStr = selectedDateObj.fullDate;\n\t\t\t\tlet startTimeStr = `${dateStr} ${selectedTimeObj.time1}`;\n\t\t\t\tlet endTimeStr = `${dateStr} ${selectedTimeObj.time2}`;\n\n\t\t\t\t// 按serviceId分组构建请求参数\n\t\t\t\tconst cartOrderings = [];\n\n\t\t\t\t// 为每个分组构建一个订单项\n\t\t\t\tthis.groupedCartItems.forEach(group => {\n\t\t\t\t\tcartOrderings.push({\n\t\t\t\t\t\taddressId: this.mrAddress.id,\n\t\t\t\t\t\tcarIds: group.items.map(item => item.id), // 收集该serviceId下所有商品的id\n\t\t\t\t\t\tendTime: endTimeStr,\n\t\t\t\t\t\tserviceId: group.serviceId,\n\t\t\t\t\t\tstartTime: startTimeStr,\n\t\t\t\t\t\ttext: this.notes || \"无\", // 如果没有备注则默认为\"无\"\n\t\t\t\t\t\ttype: 1, // 默认type为1\n\t\t\t\t\t\turgent: group.urgent || 0 // 使用分组的urgent值\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 构建最终请求参数\n\t\t\t\tconst requestData = {\n\t\t\t\t\tcartOrderings: cartOrderings\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\tconsole.log('Submitting order data:', requestData);\n\n\t\t\t\t// 发送请求\n\t\t\t\tthis.$api.service.submitCartOrder(requestData).then(res => {\n\t\t\t\t\tif (res.code === \"200\") {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\ttitle: '订单提交成功',\n\t\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tthis.isSubmitting = false;\n\t\t\t\t\t\t\t// 使用 redirectTo 跳转到订单列表页面，这样返回时不会回到当前页面\n\t\t\t\t\t\t\tuni.redirectTo({\n\t\t\t\t\t\t\t\turl: '../user/order_list?tab=0&refresh=1&from=cart_play'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.isSubmitting = false;\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: res.msg || '提交失败，请重试',\n\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tconsole.error('提交订单失败:', err);\n\t\t\t\t\tthis.isSubmitting = false;\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '网络错误，请重试',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 切换加急状态\n\t\t\ttoggleUrgent(item) {\n\t\t\t\t// 如果urgent不存在，初始化为0\n\t\t\t\tif (typeof item.urgent === 'undefined') {\n\t\t\t\t\tthis.$set(item, 'urgent', 0);\n\t\t\t\t}\n\t\t\t\t// 切换状态 0 -> 1 或 1 -> 0\n\t\t\t\tthis.$set(item, 'urgent', item.urgent === 1 ? 0 : 1);\n\t\t\t\tconsole.log(`商品${item.id}加急状态: ${item.urgent}`);\n\t\t\t},\n\t\t\t// 切换分组加急状态\n\t\t\ttoggleGroupUrgent(group) {\n\t\t\t\t// 切换状态 0 -> 1 或 1 -> 0\n\t\t\t\tthis.$set(group, 'urgent', group.urgent === 1 ? 0 : 1);\n\t\t\t\tconsole.log(`服务${group.serviceId}加急状态: ${group.urgent}`);\n\t\t\t}\n\t\t},\n\t\tasync onLoad(options) {\n\t\tconsole.log(options); // Check the received options\n\t\t   let ids = '';\n\t\t       if (options.ids) {\n\t\t           try {\n\t\t               ids = decodeURIComponent(options.ids); // e.g., \"53,62\"\n\t\t               const res = await this.$api.service.getcartinfo({ ids });\n\t\t               console.log('API response:', res);\n\n\t\t               // Process cart data\n\t\t               if (res && res.code === \"200\" && res.data && res.data.length > 0) {\n\t\t                   this.cartItems = res.data;\n\n\t\t                   // Get detailed information for all services\n\t\t                   const serviceIds = [...new Set(res.data.map(item => item.serviceId))];\n\t\t                   await this.getServicesInfo(serviceIds);\n\t\t               }\n\t\t           } catch (e) {\n\t\t               console.error('Failed to process ids or API call failed:', e);\n\t\t           }\n\t\t       } else {\n\t\t           console.error('No ids provided in options');\n\t\t       }\n\n\t\t       // Initialize other data\n\t\t       this.getDefaultAddress();\n\t\t       this.getTime();\n\t\t},\n\n\t\tonShow() {\n\t\t\tlet that = this;\n\t\t\tuni.$once('chooseAddress', function(e) {\n\t\t\t\tthat.mrAddress = e;\n\t\t\t});\n\t\t}\n\t}\n</script>\n\n<style scoped lang=\"scss\">\n.page {\n\tpadding-bottom: 200rpx;\n\n\t::v-deep .u-popup__content {\n\t\tdisplay: none;\n\t}\n\n\t::v-deep .u-number-box__plus {\n\t\tborder-radius: 50%;\n\t\twidth: 36rpx;\n\t\theight: 36rpx !important;\n\t\tbackground-color: #fff !important;\n\t\tborder: 1px solid #000;\n\n\t\ttext {\n\t\t\tfont-size: 24rpx !important;\n\t\t\tline-height: 36rpx !important;\n\t\t}\n\t}\n\n\t::v-deep .u-number-box__minus {\n\t\tborder-radius: 50%;\n\t\twidth: 36rpx;\n\t\theight: 36rpx !important;\n\t\tbackground-color: #fff !important;\n\t\tborder: 1px solid #000;\n\n\t\ttext {\n\t\t\tfont-size: 24rpx !important;\n\t\t\tline-height: 36rpx !important;\n\t\t}\n\t}\n\n\t::v-deep .u-number-box__minus--disabled {\n\t\tborder: 1px solid #ADADAD;\n\t}\n\n\t::v-deep .u-number-box__input {\n\t\tbackground-color: #fff !important;\n\t}\n\n\t.service-divider {\n\t\theight: 2rpx;\n\t\tbackground-color: #EEEEEE;\n\t\tmargin: 20rpx 0;\n\t\twidth: 100%;\n\t}\n\n\t.choose_time {\n\t\tpadding-top: 40rpx;\n\t\twidth: 750rpx;\n\t\theight: 920rpx;\n\t\tbackground: #FFFFFF;\n\t\tborder-radius: 40rpx 40rpx 0rpx 0rpx;\n\t\topacity: 1;\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\tz-index: 10088;\n\t\ttransition: all 0.5s;\n\n\t\t.head {\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #171717;\n\t\t\ttext-align: center;\n\t\t}\n\n\t\t.close {\n\t\t\tposition: absolute;\n\t\t\ttop: 44rpx;\n\t\t\tright: 32rpx;\n\n\t\t\timage {\n\t\t\t\twidth: 37rpx;\n\t\t\t\theight: 37rpx;\n\t\t\t}\n\t\t}\n\n\t\t.date {\n\t\t\tmargin-top: 40rpx;\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-around;\n\t\t\talign-items: center;\n\n\t\t\t.date_item {\n\t\t\t\ttext-align: center;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #171717;\n\n\t\t\t\t.hk {\n\t\t\t\t\tmargin-top: 8rpx;\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\theight: 6rpx;\n\t\t\t\t\tbackground: #2E80FE;\n\t\t\t\t\tborder-radius: 4rpx 4rpx 4rpx 4rpx;\n\t\t\t\t\topacity: 1;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.time_all {\n\t\t\tmargin-top: 10rpx;\n\t\t\twidth: 750rpx;\n\t\t\theight: 520rpx;\n\t\t\tbackground: #F7F7F7;\n\t\t\tpadding: 20rpx 10rpx;\n\n\t\t\t.time_columns {\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: space-around;\n\n\t\t\t\t.time_column {\n\t\t\t\t\twidth: 330rpx;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tflex-direction: column;\n\t\t\t\t\tgap: 10rpx;\n\n\t\t\t\t\t.time_item {\n\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\theight: 80rpx;\n\t\t\t\t\t\tbackground: #FFFFFF;\n\t\t\t\t\t\tborder-radius: 16rpx;\n\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\tcolor: #333333;\n\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\tline-height: 80rpx;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.btn {\n\t\t\tmargin: 0 auto;\n\t\t\tmargin-top: 28rpx;\n\t\t\twidth: 686rpx;\n\t\t\theight: 98rpx;\n\t\t\tbackground: #2E80FE;\n\t\t\tborder-radius: 12rpx 12rpx 12rpx 12rpx;\n\t\t\topacity: 1;\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #FFFFFF;\n\t\t\ttext-align: center;\n\t\t\tline-height: 98rpx;\n\t\t}\n\t}\n\n\t.footer {\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\twidth: 100%;\n\t\theight: 202rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 0 30rpx;\n\t\tbackground-color: #fff;\n\n\t\t.left {\n\t\t\tfont-size: 40rpx;\n\t\t\tfont-weight: 600;\n\t\t\tcolor: #E72427\n\t\t}\n\n\t\t.mid {\n\t\t\twidth: fit-content;\n\t\t\theight: 98rpx;\n\t\t\tborder-radius: 40rpx;\n\t\t\tfont-size: 26rpx;\n\t\t\tcolor: #2E80FE;\n\t\t\tline-height: 98rpx;\n\t\t\ttext-align: center;\n\t\t\tfont-weight: 700;\n\t\t\tborder: 2rpx solid #2E80FE;\n\t\t\tpadding: 0 15rpx;\n\t\t}\n\n\t\t.right {\n\t\t\twidth: 294rpx;\n\t\t\theight: 98rpx;\n\t\t\tbackground: #2E80FE;\n\t\t\tborder-radius: 50rpx 50rpx 50rpx 50rpx;\n\t\t\topacity: 1;\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #FFFFFF;\n\t\t\tline-height: 98rpx;\n\t\t\ttext-align: center;\n\t\t}\n\n\t\t.disabled {\n\t\t\topacity: 0.6;\n\t\t\tpointer-events: none;\n\t\t}\n\t}\n\n\t.fg {\n\t\theight: 20rpx;\n\t\tbackground-color: #F3F4F5;\n\t}\n\n\t.address {\n\t\theight: 164rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 0 32rpx;\n\n\t\t.left {\n\t\t\t.top {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\n\t\t\t\timage {\n\t\t\t\t\twidth: 36rpx;\n\t\t\t\t\theight: 36rpx;\n\t\t\t\t\tmargin-right: 20rpx;\n\t\t\t\t}\n\n\t\t\t\ttext {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: #171717;\n\t\t\t\t\tmax-width: 400rpx;\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.bottom {\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #ADADAD;\n\t\t\t\tpadding-left: 56rpx;\n\t\t\t\tmargin-top: 12rpx;\n\t\t\t}\n\t\t}\n\t}\n\n\t.time {\n\t\tborder-top: 2rpx solid #F0F0F0;\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\theight: 120rpx;\n\t\tpadding: 0 32rpx;\n\n\t\t.left {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\n\t\t\timage {\n\t\t\t\twidth: 36rpx;\n\t\t\t\theight: 36rpx;\n\t\t\t\tmargin-right: 20rpx;\n\t\t\t}\n\n\t\t\ttext {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #2E80FE;\n\t\t\t}\n\t\t}\n\t}\n\n\t.main {\n\t\tpadding: 20rpx 32rpx;\n\n\t\t.service-divider {\n\t\t\theight: 2rpx;\n\t\t\tbackground-color: #EEEEEE;\n\t\t\tmargin: 30rpx 0;\n\t\t\twidth: 100%;\n\t\t}\n\n\t\t.group-header {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t\tmargin-bottom: 20rpx;\n\t\t\tpadding: 0 20rpx;\n\t\t}\n\n\t\t.group-title {\n\t\t\tfont-size: 30rpx;\n\t\t\tfont-weight: 600;\n\t\t\tcolor: #333;\n\t\t}\n\n\t\t.urgent-option {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t}\n\n\t\t.urgent-text {\n\t\t\tfont-size: 24rpx;\n\t\t\tcolor: #333;\n\t\t\tmargin-left: 4rpx;\n\t\t}\n\n\t\t.service-items {\n\t\t\tbackground-color: #FFFFFF;\n\t\t\tborder-radius: 12rpx;\n\t\t\tpadding: 20rpx;\n\t\t\tmargin-bottom: 20rpx;\n\t\t\tbox-shadow: 0rpx 0rpx 8rpx 2rpx rgba(0, 0, 0, 0.05);\n\t\t}\n\n\t\t.main_item {\n\t\t\tdisplay: flex;\n\t\t\tmargin-bottom: 30rpx;\n\t\t}\n\n\t\t.main_item:last-child {\n\t\t\tmargin-bottom: 0;\n\t\t}\n\n\t\t.main_item image {\n\t\t\twidth: 160rpx;\n\t\t\theight: 160rpx;\n\t\t\tmargin-right: 20rpx;\n\t\t}\n\n\t\t.main_item .right {\n\t\t\tflex: 1;\n\t\t}\n\n\t\t.main_item .title {\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #171717;\n\t\t\tmax-width: 450rpx;\n\t\t\toverflow: hidden;\n\t\t\twhite-space: nowrap;\n\t\t\ttext-overflow: ellipsis;\n\t\t}\n\n\t\t.main_item .selected-options {\n\t\t\tmargin-top: 10rpx;\n\t\t\tfont-size: 24rpx;\n\t\t\tcolor: #ADADAD;\n\t\t\tdisplay: flex;\n\t\t\tflex-wrap: wrap;\n\t\t}\n\n\t\t.main_item .selected-label {\n\t\t\tmargin-right: 4rpx;\n\t\t}\n\n\t\t.main_item .selected-value {\n\t\t\tmargin-right: 4rpx;\n\t\t}\n\n\t\t.main_item .price {\n\t\t\tmargin-top: 40rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: space-between;\n\t\t}\n\n\t\t.main_item .price text {\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #2E80FE;\n\t\t}\n\t}\n\n\t.notes {\n\t\tpadding: 40rpx 32rpx;\n\n\t\t.title {\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #333333;\n\t\t}\n\n\t\ttextarea {\n\t\t\tmargin-top: 40rpx;\n\t\t\tpadding: 40rpx 30rpx;\n\t\t\tbox-sizing: border-box;\n\t\t\twidth: 686rpx;\n\t\t\theight: 242rpx;\n\t\t\tbackground: #F7F7F7;\n\t\t\tborder-radius: 20rpx 20rpx 20rpx 20rpx;\n\t\t\topacity: 1;\n\t\t}\n\t}\n}\n.group-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.group-title {\n  font-size: 30rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.urgent-option {\n  display: flex;\n  align-items: center;\n}\n\n.urgent-text {\n  font-size: 24rpx;\n  color: #333;\n  margin-left: 4rpx;\n}\n</style>\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cart_play.vue?vue&type=style&index=0&id=a95b2d86&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cart_play.vue?vue&type=style&index=0&id=a95b2d86&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756166576852\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}