{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/skills.vue?c988", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/skills.vue?79af", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/skills.vue?3593", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/skills.vue?7ccd", "uni-app:///shifu/skills.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/skills.vue?5a6f", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/skills.vue?cb47"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "keyword", "categories", "selectedCategoryId", "expandedSubCategories", "loading", "shifuInfo", "serviceIds", "error", "dataBox", "computed", "currentCategory", "console", "methods", "selectCategory", "toggleSubCategory", "toggleSelectService", "selectedItems", "count", "isServiceSelected", "getSelectedCount", "selectAllServices", "allServiceIds", "isAllSelected", "saveSettings", "serviceIdsString", "uni", "userId", "mobile", "shifuInfoToSend", "res", "title", "icon", "duration", "success", "setTimeout", "delta", "goUrl", "url", "getList", "$api", "response", "categoriesData", "Array", "category", "firstCategory", "loadSavedSelections", "split", "map", "filter", "subCategory", "getInfoS", "serviceIdsArray", "onLoad", "city"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACqC;;;AAG1F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7DA;AAAA;AAAA;AAAA;AAAq1B,CAAgB,q2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACgGz2B;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;QAAAC;MAAA;MAAA;MACAC;MACAC;IACA;EACA;;EACAC;IACAC;MAAA;MACA;MACA;QAAA;MAAA;MACAC;MACA;IACA;EACA;EACAC;IACA;IACAC;MACAF;MACA;;MAEA;MACA;QAAA;MAAA;MACA;QACA;MACA;MAEA;IACA;IAEA;IACAG;MACAH;MACA;MAEA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAI;MACAJ;;MAEA;MACA;QACA;UAAAL;QAAA;MACA;;MAEA;MACA;QACA;UACAU;UACAC;QACA;MACA;MAEA;MAEA;QACA;QACA;UACA;QACA;MACA;QACA;QACA;UAAA;QAAA;MACA;;MAEA;MACA;MAEAN;MACA;IACA;IAEA;IACAO;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QAAA;MAAA;MACA;MAEA;QAAA;MAAA;MACA;MAEA;QACA;QACA;QACA;UAAA;QAAA;MACA;QACA;QACAC;UACA;YACA;UACA;UACA;YACA;UACA;QACA;MACA;;MAEA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QAAA;MAAA;MACA;MACA;QAAA;MAAA;MACA;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC;gBACAC;gBACAd;;gBAEA;gBACA;kBACA;oBAAAL;kBAAA;gBACA;gBACAoB;gBACAC,4CACA;gBACAC,kDACA;kBACAF;kBACAC;kBACArB;gBAAA;gBAGAK;gBAAA;gBAAA,OACA;cAAA;gBAAAkB;gBACAlB;gBAEA;kBACAc;oBACAK;oBACAC;oBACAC;oBACAC;sBACAC;wBACAT;0BAAAU;wBAAA;sBACA;oBACA;kBACA;gBACA;kBACAV;oBACAK;oBACAC;kBACA;kBACAG;oBACAT;sBAAAU;oBAAA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAV;kBACAK;kBACAC;gBACA;gBACApB;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAyB;MACAX;QAAAY;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAC;gBACA7B;;gBAEA;gBACA8B;gBAAA,KACAC;kBAAA;kBAAA;gBAAA;gBACAD;gBAAA;gBAAA;cAAA;gBAAA,MACAD;kBAAA;kBAAA;gBAAA;gBACAC;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAGA;gBACAA;kBACA;kBACAE;oBACA;oBACA;sBACA;wBACA3B;wBACAC;sBACA;oBACA;kBACA;gBACA;gBAEA;gBACAN;gBAEA;kBACA;kBACAiC;kBACA;oBACA;kBACA;gBACA;kBACA;kBACAnB;oBACAK;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACApB;gBACAc;kBACAK;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAc;MAAA;MACA;QACA;QACA;UACA,sCACAC,WACAC;YAAA;UAAA,GACAC;YAAA;UAAA;QACA;UACA;QACA;;QAEA;QACA;UACA;YACAL;cACA;gBACA;kBACA;oBACA3B;oBACAC;kBACA;gBACA;gBACA;kBAAA,OACAgC;oBAAA;kBAAA;gBAAA,EACA;gBACA;gBACA;cACA;YACA;UACA;QACA;QAEAtC;QACAA;QACA;MACA;QACAA;QACA;MACA;IACA;IAEA;IACAuC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAX;cAAA;gBAAAV;gBACAlB;;gBAEA;gBACA;kBAAAL;gBAAA;;gBAEA;gBACA6C;gBACA;kBACAA,iCACAL,WACAC;oBAAA;kBAAA,GACAC;oBAAA;kBAAA;gBACA;kBACAG;oBAAA;kBAAA;oBAAA;kBAAA;gBACA;gBACA;;gBAEA;gBACA;kBACA;gBACA;gBAEAxC;;gBAEA;gBACA;gBACA;kBACA;oBACAgC;sBACA;wBACA;0BACA3B;0BACAC;wBACA;wBACA;0BAAA,OACAgC;4BAAA;0BAAA;wBAAA,EACA;wBACA;wBACA;sBACA;oBACA;kBACA;gBACA;gBAEAtC;gBACAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;kBAAAL;gBAAA;gBACA;gBACAmB;kBACAK;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;EACAqB;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAEAC;cACA1C;cACA;cACAc;cAAA;cAAA,OACA;YAAA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAd;cACAc;gBACAK;gBACAC;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACvdA;AAAA;AAAA;AAAA;AAAiuC,CAAgB,otCAAG,EAAC,C;;;;;;;;;;;ACArvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shifu/skills.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './shifu/skills.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./skills.vue?vue&type=template&id=2b3aa6dc&scoped=true&\"\nvar renderjs\nimport script from \"./skills.vue?vue&type=script&lang=js&\"\nexport * from \"./skills.vue?vue&type=script&lang=js&\"\nimport style0 from \"./skills.vue?vue&type=style&index=0&id=2b3aa6dc&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2b3aa6dc\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shifu/skills.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./skills.vue?vue&type=template&id=2b3aa6dc&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.loading && !_vm.error ? _vm.categories.length : null\n  var g1 =\n    !_vm.loading && !_vm.error\n      ? _vm.currentCategory &&\n        _vm.currentCategory.children &&\n        _vm.currentCategory.children.length\n      : null\n  var l1 =\n    !_vm.loading && !_vm.error && g1\n      ? _vm.__map(_vm.currentCategory.children, function (subCategory, __i1__) {\n          var $orig = _vm.__get_orig(subCategory)\n          var m0 = _vm.getSelectedCount(subCategory.id)\n          var m1 = _vm.isAllSelected(subCategory.id)\n          var g2 = _vm.expandedSubCategories.includes(subCategory.id)\n          var g3 =\n            _vm.expandedSubCategories.includes(subCategory.id) &&\n            subCategory.serviceList &&\n            subCategory.serviceList.length\n          var l0 = g3\n            ? _vm.__map(subCategory.serviceList, function (service, __i2__) {\n                var $orig = _vm.__get_orig(service)\n                var m2 = _vm.isServiceSelected(service.id, subCategory.id)\n                return {\n                  $orig: $orig,\n                  m2: m2,\n                }\n              })\n            : null\n          var g4 = !g3\n            ? _vm.expandedSubCategories.includes(subCategory.id) &&\n              (!subCategory.serviceList || !subCategory.serviceList.length)\n            : null\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n            g2: g2,\n            g3: g3,\n            l0: l0,\n            g4: g4,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./skills.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./skills.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page\">\n    <view class=\"main\">\n      <!-- 左侧父类分类列表 -->\n      <view class=\"left\">\n        <scroll-view scroll-y=\"true\" class=\"scrollL\">\n          <view v-if=\"loading\" class=\"loading\">\n            <text>加载中...</text>\n          </view>\n          <view v-else-if=\"error\" class=\"error\">\n            <text>{{ error }}</text>\n          </view>\n          <view v-else-if=\"!categories.length\" class=\"no-content\">\n            <text>暂无分类数据</text>\n          </view>\n          <view\n            v-else\n            class=\"left_item\"\n            v-for=\"category in categories\"\n            :key=\"category.id\"\n            @tap=\"selectCategory(category.id)\"\n            :class=\"{ active: selectedCategoryId === category.id }\"\n          >\n            <view class=\"category_name\">{{ category.name }}</view>\n          </view>\n        </scroll-view>\n      </view>\n\n      <!-- 右侧子类和服务列表 -->\n      <view class=\"right\">\n        <scroll-view scroll-y=\"true\" class=\"scrollR\">\n          <view v-if=\"loading\" class=\"loading\">\n            <text>加载中...</text>\n          </view>\n          <view v-else-if=\"error\" class=\"error\">\n            <text>{{ error }}</text>\n          </view>\n          <view v-else-if=\"currentCategory && currentCategory.children && currentCategory.children.length\">\n            <!-- 循环子类分类 -->\n            <view \n              class=\"subcategory_section\" \n              v-for=\"subCategory in currentCategory.children\" \n              :key=\"subCategory.id\"\n            >\n              <!-- 子类标题和已选择计数 -->\n              <view class=\"subcategory_header\">\n                <view class=\"subcategory_title\" @click=\"toggleSubCategory(subCategory.id)\">\n                  {{ subCategory.name }} \n                  <text class=\"selected_count\">(已选择{{ getSelectedCount(subCategory.id) }})</text>\n                </view>\n                <view class=\"select_all\" @click=\"selectAllServices(subCategory.id)\">\n                  {{ isAllSelected(subCategory.id) ? '取消全选' : '全选' }}\n                </view>\n                <view class=\"expand_icon\" @click=\"toggleSubCategory(subCategory.id)\">\n                  {{ expandedSubCategories.includes(subCategory.id) ? '▲' : '▼' }}\n                </view>\n              </view>\n              \n              <!-- 服务列表 -->\n              <view \n                class=\"service_items\" \n                v-if=\"expandedSubCategories.includes(subCategory.id) && subCategory.serviceList && subCategory.serviceList.length\"\n              >\n                <view \n                  class=\"service_item\" \n                  v-for=\"service in subCategory.serviceList\" \n                  :key=\"service.id\"\n                  @click=\"toggleSelectService(service.id, subCategory.id)\"\n                  :class=\"{ active: isServiceSelected(service.id, subCategory.id) }\"\n                >\n                  {{ service.title }}\n                </view>\n              </view>\n              \n              <!-- 如果没有服务项目 -->\n              <view \n                class=\"no-services\" \n                v-else-if=\"expandedSubCategories.includes(subCategory.id) && (!subCategory.serviceList || !subCategory.serviceList.length)\"\n              >\n                <text>暂无服务项目</text>\n              </view>\n            </view>\n          </view>\n          <view v-else class=\"no-content\">\n            <text>暂无子分类</text>\n          </view>\n        </scroll-view>\n      </view>\n    </view>\n    <view class=\"footer\">\n      <button class=\"save_btn\" @click=\"saveSettings\">保存设置</button>\n    </view>\n  </view>\n</template>\n\n<script>\nimport $api from \"@/api/index.js\";\n\nexport default {\n  data() {\n    return {\n      keyword: \"\",\n      categories: [],\n      selectedCategoryId: null,\n      expandedSubCategories: [], // 存储展开的子类ID\n      loading: false,\n      shifuInfo: { serviceIds: [] }, // Initialize with default serviceIds\n      error: null,\n      dataBox: {}, // 存储用户选择的服务项目，按子类ID分组\n    };\n  },\n  computed: {\n    currentCategory() {\n      if (!this.selectedCategoryId) return null;\n      const category = this.categories.find(cat => cat.id === this.selectedCategoryId);\n      console.log(\"Current category:\", category);\n      return category;\n    }\n  },\n  methods: {\n    // 选择父类分类\n    selectCategory(id) {\n      console.log(\"选择父类分类:\", id);\n      this.selectedCategoryId = id;\n      \n      // 初始时展开第一个子类\n      const category = this.categories.find(cat => cat.id === id);\n      if (category && category.children && category.children.length > 0) {\n        this.expandedSubCategories = [category.children[0].id];\n      }\n      \n      this.$forceUpdate();\n    },\n    \n    // 切换子类展开/折叠状态\n    toggleSubCategory(subCategoryId) {\n      console.log(\"切换子类展开状态:\", subCategoryId);\n      const index = this.expandedSubCategories.indexOf(subCategoryId);\n      \n      if (index === -1) {\n        this.expandedSubCategories.push(subCategoryId);\n      } else {\n        this.expandedSubCategories.splice(index, 1);\n      }\n    },\n    \n    // 切换服务项选择状态\n    toggleSelectService(serviceId, subCategoryId) {\n      console.log(\"切换服务选择状态:\", serviceId, subCategoryId);\n      \n      // Ensure shifuInfo is initialized\n      if (!this.shifuInfo) {\n        this.shifuInfo = { serviceIds: [] };\n      }\n      \n      // Ensure the subcategory exists in dataBox\n      if (!this.dataBox[subCategoryId]) {\n        this.$set(this.dataBox, subCategoryId, {\n          selectedItems: [],\n          count: 0\n        });\n      }\n      \n      const index = this.dataBox[subCategoryId].selectedItems.indexOf(serviceId);\n      \n      if (index === -1) {\n        this.dataBox[subCategoryId].selectedItems.push(serviceId);\n        if (!this.shifuInfo.serviceIds.includes(serviceId)) {\n          this.shifuInfo.serviceIds.push(serviceId);\n        }\n      } else {\n        this.dataBox[subCategoryId].selectedItems.splice(index, 1);\n        this.shifuInfo.serviceIds = this.shifuInfo.serviceIds.filter(id => id !== serviceId);\n      }\n      \n      // Update count\n      this.dataBox[subCategoryId].count = this.dataBox[subCategoryId].selectedItems.length;\n      \n      console.log(\"Updated shifuInfo.serviceIds:\", this.shifuInfo.serviceIds);\n      this.$forceUpdate();\n    },\n    \n    // 检查服务是否被选中\n    isServiceSelected(serviceId, subCategoryId) {\n      if (!this.dataBox[subCategoryId]) return false;\n      return this.dataBox[subCategoryId].selectedItems.includes(serviceId);\n    },\n    \n    // 获取子类选中的服务数量\n    getSelectedCount(subCategoryId) {\n      if (!this.dataBox[subCategoryId]) return 0;\n      return this.dataBox[subCategoryId].count || 0;\n    },\n    \n    // 全选/取消全选服务项\n    selectAllServices(subCategoryId) {\n      const subCategory = this.currentCategory.children.find(sub => sub.id === subCategoryId);\n      if (!subCategory || !subCategory.serviceList || !subCategory.serviceList.length) return;\n\n      const allServiceIds = subCategory.serviceList.map(service => service.id);\n      const isAllCurrentlySelected = this.isAllSelected(subCategoryId);\n\n      if (isAllCurrentlySelected) {\n        // 取消全选\n        this.dataBox[subCategoryId].selectedItems = [];\n        this.shifuInfo.serviceIds = this.shifuInfo.serviceIds.filter(id => !allServiceIds.includes(id));\n      } else {\n        // 全选\n        allServiceIds.forEach(serviceId => {\n          if (!this.dataBox[subCategoryId].selectedItems.includes(serviceId)) {\n            this.dataBox[subCategoryId].selectedItems.push(serviceId);\n          }\n          if (!this.shifuInfo.serviceIds.includes(serviceId)) {\n            this.shifuInfo.serviceIds.push(serviceId);\n          }\n        });\n      }\n\n      // 更新计数\n      this.dataBox[subCategoryId].count = this.dataBox[subCategoryId].selectedItems.length;\n      this.$forceUpdate();\n    },\n    \n    // 检查是否所有服务项都已选中\n    isAllSelected(subCategoryId) {\n      const subCategory = this.currentCategory.children.find(sub => sub.id === subCategoryId);\n      if (!subCategory || !subCategory.serviceList || !subCategory.serviceList.length) return false;\n      const allServiceIds = subCategory.serviceList.map(service => service.id);\n      const selectedServiceIds = this.dataBox[subCategoryId]?.selectedItems || [];\n      return allServiceIds.length > 0 && allServiceIds.every(id => selectedServiceIds.includes(id));\n    },\n    \n    // 保存设置\n    async saveSettings() {\n      try {\n        // Save shifuInfo.serviceIds as a comma-separated string\n        const serviceIdsString = this.shifuInfo.serviceIds.join(',');\n        uni.setStorageSync('selectedServices', serviceIdsString);\n        console.log(\"Saved selectedServices:\", serviceIdsString);\n        \n        // Ensure shifuInfo is initialized\n        if (!this.shifuInfo) {\n          this.shifuInfo = { serviceIds: [] };\n        }\n        let userId = uni.getStorageSync('userId') || '';\n        let mobile = uni.getStorageSync('phone') || '';\n        // Prepare shifuInfo with serviceIds as a comma-separated string\n        const shifuInfoToSend = {\n          ...this.shifuInfo,\n          userId: userId,\n          mobile: mobile,\n          serviceIds: this.shifuInfo.serviceIds.join(',')\n        };\n        \n        console.log(\"Saving shifuInfo:\", shifuInfoToSend);\n        const res = await this.$api.shifu.updataSkill(JSON.stringify(shifuInfoToSend));\n        console.log(\"API Response from updataInfoSF:\", res);\n        \n        if (res === '信息修改成功，请等待审核') {\n          uni.showToast({\n            title: '信息修改成功，请等待审核',\n            icon: 'success',\n            duration: 2000,\n            success: () => {\n              setTimeout(() => {\n                uni.navigateBack({ delta: 1 });\n              }, 2000);\n            }\n          });\n        } else {\n          uni.showToast({\n            title: '保存成功',\n            icon: 'success'\n          });\r\n\t\t  setTimeout(() => {\r\n\t\t    uni.navigateBack({ delta: 1 });\r\n\t\t  }, 2000);\n        }\n      } catch (e) {\n        uni.showToast({\n          title: '保存失败',\n          icon: 'none'\n        });\n        console.error('保存失败:', e);\n      }\n    },\n    goUrl(url) {\n      uni.navigateTo({ url });\n    },\n    \n    // 获取分类列表\n    async getList() {\n      this.loading = true;\n      this.error = null;\n      try {\n        const response = await $api.shifu.getSkill();\n        console.log(\"API Response:\", response);\n\n        // 处理响应数据\n        let categoriesData = [];\n        if (Array.isArray(response)) {\n          categoriesData = response;\n        } else if (response.data && Array.isArray(response.data)) {\n          categoriesData = response.data;\n        } else {\n          throw new Error(\"无效或空的数据\");\n        }\n        \n        // 确保children和serviceList存在，并初始化dataBox\n        categoriesData.forEach(category => {\n          if (!category.children) category.children = [];\n          category.children.forEach(subCategory => {\n            if (!subCategory.serviceList) subCategory.serviceList = [];\n            if (!this.dataBox[subCategory.id]) {\n              this.$set(this.dataBox, subCategory.id, {\n                selectedItems: [],\n                count: 0\n              });\n            }\n          });\n        });\n        \n        this.categories = categoriesData;\n        console.log(\"Categories processed:\", this.categories);\n        \n        if (this.categories.length > 0) {\n          this.selectedCategoryId = this.categories[0].id;\n          const firstCategory = this.categories[0];\n          if (firstCategory.children && firstCategory.children.length > 0) {\n            this.expandedSubCategories = [firstCategory.children[0].id];\n          }\n        } else {\n          this.error = \"分类数据为空\";\n          uni.showToast({\n            title: \"分类数据为空\",\n            icon: \"none\"\n          });\n        }\n      } catch (err) {\n        this.error = \"数据加载失败: \" + err.message;\n        console.error(\"Error in getList:\", err);\n        uni.showToast({\n          title: this.error,\n          icon: \"none\"\n        });\n      } finally {\n        this.loading = false;\n      }\n    },\n    \n    // 加载保存的选择（仅作为API失败的备用）\n    loadSavedSelections() {\n      try {\n        const savedData = uni.getStorageSync('selectedServices');\n        if (savedData && savedData.trim()) {\n          this.shifuInfo.serviceIds = savedData\n            .split(',')\n            .map(id => parseInt(id.trim(), 10))\n            .filter(id => !isNaN(id));\n        } else {\n          this.shifuInfo.serviceIds = [];\n        }\n        \n        // Reconstruct dataBox from serviceIds\n        this.categories.forEach(category => {\n          if (category.children && category.children.length) {\n            category.children.forEach(subCategory => {\n              if (subCategory.serviceList && subCategory.serviceList.length) {\n                if (!this.dataBox[subCategory.id]) {\n                  this.$set(this.dataBox, subCategory.id, {\n                    selectedItems: [],\n                    count: 0\n                  });\n                }\n                const matchingServiceIds = this.shifuInfo.serviceIds.filter(serviceId =>\n                  subCategory.serviceList.some(service => service.id === serviceId)\n                );\n                this.dataBox[subCategory.id].selectedItems = matchingServiceIds;\n                this.dataBox[subCategory.id].count = matchingServiceIds.length;\n              }\n            });\n          }\n        });\n        \n        console.log(\"Loaded shifuInfo.serviceIds from storage:\", this.shifuInfo.serviceIds);\n        console.log(\"Reconstructed dataBox:\", this.dataBox);\n        this.$forceUpdate();\n      } catch (e) {\n        console.error('加载已保存选择失败:', e);\n        this.shifuInfo.serviceIds = [];\n      }\n    },\n    \n    // 获取并初始化服务ID\n    async getInfoS() {\n      try {\n        const res = await $api.shifu.getSInfo();\n        console.log(\"getSInfo Response:\", res);\n        \n        // Initialize shifuInfo\n        this.shifuInfo = res && typeof res === 'object' ? res : { serviceIds: [] };\n        \n        // Always use API serviceIds if available\n        let serviceIdsArray = [];\n        if (typeof res.serviceIds === 'string' && res.serviceIds.trim() !== '') {\n          serviceIdsArray = res.serviceIds\n            .split(',')\n            .map(id => parseInt(id.trim(), 10))\n            .filter(id => !isNaN(id));\n        } else if (Array.isArray(res.serviceIds)) {\n          serviceIdsArray = res.serviceIds.map(id => parseInt(id, 10)).filter(id => !isNaN(id));\n        }\n        this.shifuInfo.serviceIds = serviceIdsArray;\n        \n        // If API provides no valid serviceIds, try local storage\n        if (!this.shifuInfo.serviceIds.length) {\n          this.loadSavedSelections();\n        }\n        \n        console.log(\"Processed Service IDs:\", this.shifuInfo.serviceIds);\n\n        // Update dataBox based on shifuInfo.serviceIds\n        this.dataBox = {};\n        this.categories.forEach(category => {\n          if (category.children && category.children.length) {\n            category.children.forEach(subCategory => {\n              if (subCategory.serviceList && subCategory.serviceList.length) {\n                this.$set(this.dataBox, subCategory.id, {\n                  selectedItems: [],\n                  count: 0\n                });\n                const matchingServiceIds = this.shifuInfo.serviceIds.filter(serviceId =>\n                  subCategory.serviceList.some(service => service.id === serviceId)\n                );\n                this.dataBox[subCategory.id].selectedItems = matchingServiceIds;\n                this.dataBox[subCategory.id].count = matchingServiceIds.length;\n              }\n            });\n          }\n        });\n\n        console.log(\"Updated dataBox:\", this.dataBox);\n        console.log(\"Updated shifuInfo.serviceIds:\", this.shifuInfo.serviceIds);\n        this.$forceUpdate();\n      } catch (err) {\n        console.error(\"Error in getInfoS:\", err);\n        this.shifuInfo = { serviceIds: [] };\n        this.loadSavedSelections(); // Fallback to local storage on API failure\n        uni.showToast({\n          title: \"加载服务信息失败\",\n          icon: \"none\"\n        });\n      }\n    }\n  },\n  async onLoad() {\n    try {\n      const city = uni.getStorageSync(\"city\");\n      console.log(\"City:\", city);\n      // Clear selectedServices to start fresh\n      uni.setStorageSync('selectedServices', '');\n      await this.getList();\n      await this.getInfoS();\n    } catch (err) {\n      console.error(\"Error in onLoad:\", err);\n      uni.showToast({\n        title: \"页面加载失败\",\n        icon: \"none\"\n      });\n    }\n  },\n};\n</script>\n\n<style scoped>\n.page {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background-color: #f8f8f8;\n  padding-bottom: 120rpx; /* 为固定footer预留空间 */\n}\n\n.main {\n  flex: 1;\n  display: flex;\n  overflow: hidden;\n}\n\n.left {\n  width: 190rpx;\n  background-color: #f8f8f8;\n}\n\n.scrollL {\n  height: 100%;\n  overflow-y: auto;\n}\n\n.left_item {\n  padding: 0 20rpx;\n  min-height: 100rpx;\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  border-left: 6rpx solid transparent;\n  transition: all 0.2s;\n  display: flex;\n  flex-direction: column;\n}\n\n.left_item.active {\n  color: #2e80fe;\n  font-size: 30rpx;\n  border-left-color: #2e80fe;\n  background-color: #fff;\n}\n\n.category_name {\n  height: 100rpx;\n  width: 100%;\n  display: flex;\n  align-items: center;\n}\n\n.right {\n  flex: 1;\n  background-color: #fff;\n  border-radius: 12rpx 12rpx 0 0;\n  margin-left: 10rpx;\n}\n\n.scrollR {\n  height: 100%;\n  overflow-y: auto;\n}\n\n.subcategory_section {\n  margin-bottom: 15rpx;\n}\n\n.subcategory_header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20rpx 30rpx;\n  background-color: #fafafa;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.subcategory_title {\n  font-size: 30rpx;\n  font-weight: 500;\n  color: #333;\n}\n\n.selected_count {\n  color: #2e80fe;\n  font-weight: normal;\n}\n\n.select_all {\n  font-size: 26rpx;\n  color: #2e80fe;\n  margin-left: 20rpx;\n}\n\n.expand_icon {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.service_items {\n  display: flex;\n  flex-wrap: wrap;\n  padding: 20rpx;\n}\n\n.service_item {\n  width: calc(33.33% - 20rpx);\n  margin: 10rpx;\n  height: 80rpx;\n  background-color: #f5f5f5;\n  border-radius: 8rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 26rpx;\n  color: #666;\n  transition: all 0.3s;\n  text-align: center;\n  padding: 0 10rpx;\n}\n\n.service_item.active {\n  background-color: #e6f0ff;\n  color: #2e80fe;\n  border: 1rpx solid #2e80fe;\n}\n\n.no-services,\n.no-content,\n.loading,\n.error {\n  text-align: center;\n  color: #999;\n  font-size: 28rpx;\n  padding: 40rpx;\n}\n\n.error {\n  color: #ff4d4f;\n}\n\n.footer {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 120rpx;\n  padding: 15rpx;\n  background-color: #fff;\n  border-top: 1rpx solid #f0f0f0;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 10;\n}\n\n.save_btn {\n  width: 90%;\n  height: 90rpx;\n  background-color: #2e80fe;\n  color: white;\n  border-radius: 45rpx;\n  font-size: 32rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./skills.vue?vue&type=style&index=0&id=2b3aa6dc&scoped=true&lang=css&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./skills.vue?vue&type=style&index=0&id=2b3aa6dc&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756166564211\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}