{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/fast.vue?62e0", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/fast.vue?6c7f", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/fast.vue?0bc7", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/fast.vue?1209", "uni-app:///pages/fast.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/fast.vue?a19a", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/fast.vue?05d3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "tabbar", "data", "serviceList", "id", "name", "displayName", "icon", "image", "showCategoryPicker", "showServicePicker", "showCategoryServicePicker", "categoryColumns", "serviceColumns", "categoryServiceColumns", "selectedCategoryData", "selectedServiceData", "currentServiceId", "currentServiceName", "methods", "selectService", "console", "city", "position", "response", "selectedCate<PERSON><PERSON>", "children", "parentId", "initialServices", "cover", "serviceCateId", "title", "uni", "<PERSON><PERSON><PERSON><PERSON>", "index", "picker", "confirmCategoryService", "confirmCategory", "confirmService", "goToDetails", "url", "fail", "closePickers"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACsC;;;AAGzF;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAm1B,CAAgB,m2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;eCyEv2B;EACAC;IACAC;EACA;EACAC;IACA;MACAC,cACA;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAE;MACA,GACA;QACAJ;QACAC;QACAC;QACAE;MACA,GACA;QACAJ;QACAC;QACAC;QACAE;MACA,EACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAEA;gBACA;gBAAA;gBAGA;gBACAC;kBAAAC;gBAAA;gBACAF;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAAG;gBACAH;gBAEA;kBACA;kBACAI;oBAAA;kBAAA;kBACAJ;kBAEA;oBACA;oBACAK;sBAAA;wBACAtB;wBACAC;wBACAsB;wBACAxB;sBACA;oBAAA,IACA;oBACAyB;sBAAA;wBACAxB;wBACAC;wBAAA;wBACAwB;wBACAC;wBACAC;sBACA;oBAAA,IAEA;;oBACA;oBAEAV;;oBAEA;oBACA;;oBAEA;oBACA;sBACA;wBACA;wBACA;sBACA;oBACA;kBACA;oBACAA;oBACAW;sBACAD;sBACAxB;oBACA;kBACA;gBACA;kBACAc;kBACAW;oBACAD;oBACAxB;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAc;gBACAW;kBACAD;kBACAxB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA0B;MACA;QAAAC;QAAA;QAAAC;MAEA;QACA;QACA;QACAd;QAEA;UACA;UACA;YAAA;cACAjB;cACAC;cAAA;cACAwB;cACAC;cACAC;YACA;UAAA;;UACAI;UACA;UACAd;QACA;UACAc;UACA;QACA;MACA;IACA;IAEA;IACAC;MACAf;MACA;MACA;MAEA;QACA;QACA;QACA;QACA;MACA;QACAW;UACAD;UACAxB;QACA;MACA;IACA;IAEA;IACA8B;MACAhB;MACA;MAEA;QACA;QACA;UAAA;YACAjB;YACAC;YACAwB;YACAC;YACAC;UACA;QAAA;QACA;;QAEA;QACA;QACA;MACA;QACAC;UACAD;UACAxB;QACA;MACA;IACA;IAEA;IACA+B;MACAjB;MACA;MAEA;QACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAkB;MACAP;QACAQ;QACAC;UACApB;UACAW;YACAD;YACAxB;UACA;QACA;MACA;IACA;IAEA;IACAmC;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7SA;AAAA;AAAA;AAAA;AAA0lD,CAAgB,8iDAAG,EAAC,C;;;;;;;;;;;ACA9mD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/fast.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/fast.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./fast.vue?vue&type=template&id=134a5055&scoped=true&\"\nvar renderjs\nimport script from \"./fast.vue?vue&type=script&lang=js&\"\nexport * from \"./fast.vue?vue&type=script&lang=js&\"\nimport style0 from \"./fast.vue?vue&type=style&index=0&id=134a5055&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"134a5055\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/fast.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fast.vue?vue&type=template&id=134a5055&scoped=true&\"", "var components\ntry {\n  components = {\n    uPicker: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-picker/u-picker\" */ \"uview-ui/components/u-picker/u-picker.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showCategoryServicePicker = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fast.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fast.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page-container\">\n\t\t<tabbar :cur=\"3\"></tabbar>\n\n\t\t<view class=\"header\">\n\t\t\t<view class=\"header-title\">请选择服务类目</view>\n\t\t</view>\n\n\t\t<view class=\"content\">\n\t\t\t<view class=\"service-list\">\n\t\t\t\t<view class=\"service-card install-card\" @tap=\"selectService(21, '安装服务')\">\n\t\t\t\t\t<view class=\"card-content\">\n\t\t\t\t\t\t<view class=\"service-text\">上门安装</view>\n\t\t\t\t\t\t<view class=\"service-icon\">\n\t\t\t\t\t\t\t<view class=\"icon-placeholder install-icon\">🔧</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"service-card repair-card\" @tap=\"selectService(22, '维修服务')\">\n\t\t\t\t\t<view class=\"card-content\">\n\t\t\t\t\t\t<view class=\"service-text\">上门维修</view>\n\t\t\t\t\t\t<view class=\"service-icon\">\n\t\t\t\t\t\t\t<image\n\t\t\t\t\t\t\t\tsrc=\"https://zskj.asia/attachment/image/666/25/05/86fe36a3384346ef8e31bcc6b06f0c43.jpg\"\n\t\t\t\t\t\t\t\tmode=\"aspectFit\"\n\t\t\t\t\t\t\t\tclass=\"icon-image\"\n\t\t\t\t\t\t\t></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"service-card clean-card\" @tap=\"selectService(23, '清洗服务')\">\n\t\t\t\t\t<view class=\"card-content\">\n\t\t\t\t\t\t<view class=\"service-text\">清洗服务</view>\n\t\t\t\t\t\t<view class=\"service-icon\">\n\t\t\t\t\t\t\t<image\n\t\t\t\t\t\t\t\tsrc=\"https://zskj.asia/attachment/image/666/25/05/9a10d8df55c64370893ae861d2ff9887.jpg\"\n\t\t\t\t\t\t\t\tmode=\"aspectFit\"\n\t\t\t\t\t\t\t\tclass=\"icon-image\"\n\t\t\t\t\t\t\t></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"service-card rescue-card\" @tap=\"selectService(46, '出行救援')\">\n\t\t\t\t\t<view class=\"card-content\">\n\t\t\t\t\t\t<view class=\"service-text\">应急维修</view>\n\t\t\t\t\t\t<view class=\"service-icon\">\n\t\t\t\t\t\t\t<image\n\t\t\t\t\t\t\t\tsrc=\"https://zskj.asia/attachment/image/666/25/05/8df2da0eaeee41828c4535f6145a375c.jpg\"\n\t\t\t\t\t\t\t\tmode=\"aspectFit\"\n\t\t\t\t\t\t\t\tclass=\"icon-image\"\n\t\t\t\t\t\t\t></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<u-picker\n\t\t\t:show=\"showCategoryServicePicker\"\n\t\t\t:columns=\"categoryServiceColumns\"\n\t\t\t@cancel=\"showCategoryServicePicker = false\"\n\t\t\t@confirm=\"confirmCategoryService\"\n\t\t\t@change=\"changeHandler\"\n\t\t\tref=\"uPicker\"\n\t\t\tkeyName=\"name\"\n\t\t\t:defaultIndex=\"[0, 0]\"\n\t\t></u-picker>\n\t</view>\n</template>\n\n<script>\nimport tabbar from \"@/components/tabbar.vue\";\n\nexport default {\n\tcomponents: {\n\t\ttabbar\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tserviceList: [\n\t\t\t\t{\n\t\t\t\t\tid: 21,\n\t\t\t\t\tname: '安装服务',\n\t\t\t\t\tdisplayName: '上门安装',\n\t\t\t\t\ticon: '🔧'\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 22,\n\t\t\t\t\tname: '维修服务',\n\t\t\t\t\tdisplayName: '上门维修',\n\t\t\t\t\timage: 'https://zskj.asia/attachment/image/666/25/05/86fe36a3384346ef8e31bcc6b06f0c43.jpg'\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 23,\n\t\t\t\t\tname: '清洗服务',\n\t\t\t\t\tdisplayName: '清洗服务',\n\t\t\t\t\timage: 'https://zskj.asia/attachment/image/666/25/05/9a10d8df55c64370893ae861d2ff9887.jpg'\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 46,\n\t\t\t\t\tname: '出行救援',\n\t\t\t\t\tdisplayName: '家政保洁',\n\t\t\t\t\timage: 'https://zskj.asia/attachment/image/666/25/05/8df2da0eaeee41828c4535f6145a375c.jpg'\n\t\t\t\t}\n\t\t\t],\n\t\t\tshowCategoryPicker: false,\n\t\t\tshowServicePicker: false,\n\t\t\tshowCategoryServicePicker: false,\n\t\t\tcategoryColumns: [[]],\n\t\t\tserviceColumns: [[]],\n\t\t\tcategoryServiceColumns: [[], []],\n\t\t\tselectedCategoryData: null,\n\t\t\tselectedServiceData: null,\n\t\t\tcurrentServiceId: null,\n\t\t\tcurrentServiceName: ''\n\t\t};\n\t},\n\tmethods: {\n\t\tasync selectService(serviceId, serviceName) {\n\t\t\tconsole.log('选择服务:', serviceId, serviceName);\n\n\t\t\tthis.currentServiceId = serviceId;\n\t\t\tthis.currentServiceName = serviceName;\n\n\t\t\ttry {\n\t\t\t\t// 获取城市位置信息\n\t\t\t\tconst city = uni.getStorageSync('city') || { position: '' };\n\t\t\t\tconsole.log('城市信息:', city);\n\n\t\t\t\t// 调用API获取服务分类数据\n\t\t\t\tconst response = await this.$api.service.setserviceCate(city.position);\n\t\t\t\tconsole.log('API响应:', response);\n\n\t\t\t\tif (response && response.code === \"200\" && response.data) {\n\t\t\t\t\t// 查找对应的服务分类\n\t\t\t\t\tconst selectedCategory = response.data.find(item => item.id === serviceId);\n\t\t\t\t\tconsole.log('找到的分类:', selectedCategory);\n\n\t\t\t\t\tif (selectedCategory && selectedCategory.children && selectedCategory.children.length > 0) {\n\t\t\t\t\t\t// 格式化分类数据，保留 name 属性\n\t\t\t\t\t\tconst children = selectedCategory.children.map(category => ({\n\t\t\t\t\t\t\tid: category.id,\n\t\t\t\t\t\t\tname: category.name,\n\t\t\t\t\t\t\tparentId: category.parentId,\n\t\t\t\t\t\t\tserviceList: category.serviceList\n\t\t\t\t\t\t}));\n\t\t\t\t\t\t// 格式化服务数据，将 title 映射到 name\n\t\t\t\t\t\tconst initialServices = (selectedCategory.children[0]?.serviceList || []).map(service => ({\n\t\t\t\t\t\t\tid: service.id,\n\t\t\t\t\t\t\tname: service.title || '未命名服务', // 确保 name 存在\n\t\t\t\t\t\t\tcover: service.cover,\n\t\t\t\t\t\t\tserviceCateId: service.serviceCateId,\n\t\t\t\t\t\t\ttitle: service.title // 保留原始 title 以供后续使用\n\t\t\t\t\t\t}));\n\n\t\t\t\t\t\t// 设置 categoryServiceColumns\n\t\t\t\t\t\tthis.categoryServiceColumns = [children, initialServices];\n\n\t\t\t\t\t\tconsole.log('设置联动选择器数据:', JSON.parse(JSON.stringify(this.categoryServiceColumns)));\n\n\t\t\t\t\t\t// 显示联动选择器\n\t\t\t\t\t\tthis.showCategoryServicePicker = true;\n\n\t\t\t\t\t\t// 强制更新 picker 数据\n\t\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\t\tif (this.$refs.uPicker) {\n\t\t\t\t\t\t\t\tthis.$refs.uPicker.setColumnValues(0, children);\n\t\t\t\t\t\t\t\tthis.$refs.uPicker.setColumnValues(1, initialServices);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.log('没有子分类或子分类为空');\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '该服务暂无子分类',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tconsole.log('API响应格式错误:', response);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取服务数据失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取服务数据失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '网络错误，请重试: ' + error.message,\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\n\t\t// 联动选择器变化处理\n\t\tchangeHandler(e) {\n\t\t\tconst { columnIndex, index, picker = this.$refs.uPicker } = e;\n\n\t\t\tif (columnIndex === 0) {\n\t\t\t\t// 选择分类时，更新服务列表\n\t\t\t\tconst selectedCategory = this.categoryServiceColumns[0][index];\n\t\t\t\tconsole.log('选择的分类:', selectedCategory);\n\n\t\t\t\tif (selectedCategory && selectedCategory.serviceList) {\n\t\t\t\t\t// 格式化服务数据，将 title 映射到 name\n\t\t\t\t\tconst services = selectedCategory.serviceList.map(service => ({\n\t\t\t\t\t\tid: service.id,\n\t\t\t\t\t\tname: service.title || '未命名服务', // 确保 name 存在\n\t\t\t\t\t\tcover: service.cover,\n\t\t\t\t\t\tserviceCateId: service.serviceCateId,\n\t\t\t\t\t\ttitle: service.title // 保留原始 title 以供后续使用\n\t\t\t\t\t}));\n\t\t\t\t\tpicker.setColumnValues(1, services);\n\t\t\t\t\tthis.categoryServiceColumns[1] = services;\n\t\t\t\t\tconsole.log('更新服务列表:', JSON.parse(JSON.stringify(services)));\n\t\t\t\t} else {\n\t\t\t\t\tpicker.setColumnValues(1, []);\n\t\t\t\t\tthis.categoryServiceColumns[1] = [];\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\t// 确认分类和服务选择\n\t\tconfirmCategoryService(e) {\n\t\t\tconsole.log('选择的分类和服务:', e);\n\t\t\tthis.selectedCategoryData = e.value[0];\n\t\t\tthis.selectedServiceData = e.value[1];\n\n\t\t\tif (this.selectedServiceData) {\n\t\t\t\t// 隐藏选择器\n\t\t\t\tthis.showCategoryServicePicker = false;\n\t\t\t\t// 跳转到详情页\n\t\t\t\tthis.goToDetails(this.selectedServiceData.id);\n\t\t\t} else {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请选择具体服务',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\n\t\t// 确认分类选择（保留原方法以防其他地方调用）\n\t\tconfirmCategory(e) {\n\t\t\tconsole.log('选择的分类:', e);\n\t\t\tthis.selectedCategoryData = e.value[0];\n\n\t\t\tif (this.selectedCategoryData && this.selectedCategoryData.serviceList && this.selectedCategoryData.serviceList.length > 0) {\n\t\t\t\t// 设置服务选择器数据\n\t\t\t\tconst services = this.selectedCategoryData.serviceList.map(service => ({\n\t\t\t\t\tid: service.id,\n\t\t\t\t\tname: service.title || '未命名服务',\n\t\t\t\t\tcover: service.cover,\n\t\t\t\t\tserviceCateId: service.serviceCateId,\n\t\t\t\t\ttitle: service.title\n\t\t\t\t}));\n\t\t\t\tthis.serviceColumns = [services];\n\n\t\t\t\t// 隐藏分类选择器并显示服务选择器\n\t\t\t\tthis.showCategoryPicker = false;\n\t\t\t\tthis.showServicePicker = true;\n\t\t\t} else {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '该分类下暂无服务',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\n\t\t// 确认服务选择（保留原方法以防其他地方调用）\n\t\tconfirmService(e) {\n\t\t\tconsole.log('选择的服务:', e);\n\t\t\tthis.selectedServiceData = e.value[0];\n\n\t\t\tif (this.selectedServiceData) {\n\t\t\t\t// 隐藏服务选择器\n\t\t\t\tthis.showServicePicker = false;\n\t\t\t\t// 跳转到详情页\n\t\t\t\tthis.goToDetails(this.selectedServiceData.id);\n\t\t\t}\n\t\t},\n\n\t\t// 跳转到详情页\n\t\tgoToDetails(id) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `../user/commodity_details?id=${id}`,\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error(\"Navigation failed:\", err);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: \"跳转失败: \" + err.errMsg,\n\t\t\t\t\t\ticon: \"none\",\n\t\t\t\t\t});\n\t\t\t\t},\n\t\t\t});\n\t\t},\n\n\t\t// 关闭选择器\n\t\tclosePickers() {\n\t\t\tthis.showCategoryPicker = false;\n\t\t\tthis.showServicePicker = false;\n\t\t\tthis.showCategoryServicePicker = false;\n\t\t}\n\t}\n};\n</script>\n\n<style scoped lang=\"scss\">\n.page-container {\n\tmin-height: 100vh;\n\tbackground-color: #f5f5f5;\n}\n\n.header {\n\tpadding: 40rpx 30rpx 30rpx;\n\tbackground-color: #fff;\n\n\t.header-title {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #333;\n\t}\n}\n\n.content {\n\tpadding: 30rpx;\n\tpadding-bottom: 150rpx; /* 为底部tabbar留出空间 */\n}\n\n.service-list {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 20rpx;\n}\n\n.service-card {\n\tborder-radius: 20rpx;\n\tpadding: 40rpx 30rpx;\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n\ttransition: all 0.3s ease;\n\n\t&:active {\n\t\ttransform: scale(0.98);\n\t}\n\n\t.card-content {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t}\n\n\t.service-text {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #333;\n\t}\n\n\t.service-icon {\n\t\twidth: 80rpx;\n\t\theight: 80rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\n\t\t.icon-image {\n\t\t\twidth: 80rpx;\n\t\t\theight: 80rpx;\n\t\t\tborder-radius: 10rpx;\n\t\t}\n\n\t\t.icon-placeholder {\n\t\t\tfont-size: 50rpx;\n\t\t}\n\t}\n}\n\n/* 不同服务卡片的背景色 */\n.repair-card {\n\tbackground: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);\n}\n\n.install-card {\n\tbackground: linear-gradient(135deg, #fff3e0 0%, #ffcc80 100%);\n\n\t.install-icon {\n\t\tcolor: #ff9800;\n\t}\n}\n\n.clean-card {\n\tbackground: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);\n}\n\n.rescue-card {\n\tbackground: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fast.vue?vue&type=style&index=0&id=134a5055&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fast.vue?vue&type=style&index=0&id=134a5055&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756166578580\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}